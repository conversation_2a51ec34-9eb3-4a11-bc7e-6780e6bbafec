
<template>
  <div>
    <div class="gva-form-box">
      <el-form :model="formData" ref="elFormRef" label-position="right" :rules="rule" label-width="80px">
        <el-form-item label="资源包ID:" prop="id">
    <el-input v-model.number="formData.id" :clearable="true" placeholder="请输入资源包ID" />
</el-form-item>
        <el-form-item label="角色ID:" prop="character_id">
    <el-input v-model.number="formData.character_id" :clearable="true" placeholder="请输入角色ID" />
</el-form-item>
        <el-form-item label="资源包类型（经典/休闲等）:" prop="type">
    <el-input v-model="formData.type" :clearable="true" placeholder="请输入资源包类型（经典/休闲等）" />
</el-form-item>
        <el-form-item label="资源包名称:" prop="name">
    <el-input v-model="formData.name" :clearable="true" placeholder="请输入资源包名称" />
</el-form-item>
        <el-form-item label="角色头像URL:" prop="avatar_url">
    <el-input v-model="formData.avatar_url" :clearable="true" placeholder="请输入角色头像URL" />
</el-form-item>
        <el-form-item label="角色背景图URL:" prop="background_url">
    <el-input v-model="formData.background_url" :clearable="true" placeholder="请输入角色背景图URL" />
</el-form-item>
        <el-form-item label="角色动态图URL:" prop="animated_url">
    <el-input v-model="formData.animated_url" :clearable="true" placeholder="请输入角色动态图URL" />
</el-form-item>
        <el-form-item label="试听音频URL:" prop="sample_audio">
    <el-input v-model="formData.sample_audio" :clearable="true" placeholder="请输入试听音频URL" />
</el-form-item>
        <el-form-item label="试听文本:" prop="sample_audio_text">
    <el-input v-model="formData.sample_audio_text" :clearable="true" placeholder="请输入试听文本" />
</el-form-item>
        <el-form-item label="是否默认:" prop="is_default">
    <el-switch v-model="formData.is_default" active-color="#13ce66" inactive-color="#ff4949" active-text="是" inactive-text="否" clearable ></el-switch>
</el-form-item>
        <el-form-item label="排序:" prop="sort">
    <el-input v-model.number="formData.sort" :clearable="true" placeholder="请输入排序" />
</el-form-item>
        <el-form-item label="状态（1:上架，2:下架）:" prop="status">
    <el-switch v-model="formData.status" active-color="#13ce66" inactive-color="#ff4949" active-text="是" inactive-text="否" clearable ></el-switch>
</el-form-item>
        <el-form-item label="创建时间:" prop="created_at">
    <el-input v-model.number="formData.created_at" :clearable="true" placeholder="请输入创建时间" />
</el-form-item>
        <el-form-item label="更新时间:" prop="updated_at">
    <el-input v-model.number="formData.updated_at" :clearable="true" placeholder="请输入更新时间" />
</el-form-item>
        <el-form-item>
          <el-button :loading="btnLoading" type="primary" @click="save">保存</el-button>
          <el-button type="primary" @click="back">返回</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import {
  createCharacterAssets,
  updateCharacterAssets,
  findCharacterAssets
} from '@/api/characters/characterAssets'

defineOptions({
    name: 'CharacterAssetsForm'
})

// 自动获取字典
import { getDictFunc } from '@/utils/format'
import { useRoute, useRouter } from "vue-router"
import { ElMessage } from 'element-plus'
import { ref, reactive } from 'vue'


const route = useRoute()
const router = useRouter()

// 提交按钮loading
const btnLoading = ref(false)

const type = ref('')
const formData = ref({
            id: undefined,
            character_id: undefined,
            type: '',
            name: '',
            avatar_url: '',
            background_url: '',
            animated_url: '',
            sample_audio: '',
            sample_audio_text: '',
            is_default: false,
            sort: undefined,
            status: 0,
            created_at: undefined,
            updated_at: undefined,
        })
// 验证规则
const rule = reactive({
})

const elFormRef = ref()

// 初始化方法
const init = async () => {
 // 建议通过url传参获取目标数据ID 调用 find方法进行查询数据操作 从而决定本页面是create还是update 以下为id作为url参数示例
    if (route.query.id) {
      const res = await findCharacterAssets({ id: route.query.id })
      if (res.code === 0) {
        formData.value = res.data
        type.value = 'update'
      }
    } else {
      type.value = 'create'
    }
}

init()
// 保存按钮
const save = async() => {
      btnLoading.value = true
      elFormRef.value?.validate( async (valid) => {
         if (!valid) return btnLoading.value = false
            let res
           switch (type.value) {
             case 'create':
               res = await createCharacterAssets(formData.value)
               break
             case 'update':
               res = await updateCharacterAssets(formData.value)
               break
             default:
               res = await createCharacterAssets(formData.value)
               break
           }
           btnLoading.value = false
           if (res.code === 0) {
             ElMessage({
               type: 'success',
               message: '创建/更改成功'
             })
           }
       })
}

// 返回按钮
const back = () => {
    router.go(-1)
}

</script>

<style>
</style>
