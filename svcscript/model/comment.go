package model

import (
	"context"
	"errors"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/util"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/common"
	"new-gitlab.xunlei.cn/vcproject/backends/svcscript/config"

	"new-gitlab.xunlei.cn/vcproject/backends/baselib/alioss"

	"gorm.io/gorm"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/errcode"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcscript"
)

// CommentModelInterface 评论模型接口
type CommentModelInterface interface {
	CreateComment(ctx context.Context, comment *Comment) (int64, error)
	GetCommentByID(ctx context.Context, id int64) (*Comment, error)
	GetComments(ctx context.Context, page, pageSize int32, commentType svcscript.CommentType, targetID, parentID, userID int64) ([]*Comment, int64, error)
	GetCommentsByIDs(ctx context.Context, userID int64, ids []int64) ([]*Comment, error)
	GetCommentsByRootID(ctx context.Context, rootCommentID, userID int64, limit int32) ([]*Comment, error)
	GetCommentsByRootIDWithPagination(ctx context.Context, rootCommentID, userID int64, page, pageSize int32) ([]*Comment, int64, error)
	BatchGetRepliedUserIDs(ctx context.Context, commentIDs []int64) (map[int64]int64, error)
	BatchGetRepliesByParentIDs(ctx context.Context, parentIDs []int64, userID int64, limit int32) ([]*Comment, error)
	UpdateComment(ctx context.Context, comment *Comment) error
	DeleteComment(ctx context.Context, id int64) error
	SetCommentTop(ctx context.Context, id int64, isTop bool) error
	SetCommentTopWithExclusive(ctx context.Context, commentID int64, scriptID int64, isTop bool) error
	BatchGetCommentCountByDubbingIDs(ctx context.Context, userID int64, dubbingIDs []int64) (map[int64]int32, error)
	BatchCountRepliesByParentIDs(ctx context.Context, userID int64, parentIDs []int64) (map[int64]int64, error)
	IncreaseLikes(ctx context.Context, id int64, count int64) error
}

// Comment 评论表结构
type Comment struct {
	ID                    int64                   `gorm:"primaryKey;column:id" json:"id"`                                // 评论ID
	CommentType           svcscript.CommentType   `gorm:"column:comment_type;not null" json:"comment_type"`              // 评论类型
	ParentID              int64                   `gorm:"column:parent_id;default:0;index" json:"parent_id"`             // 父级评论ID
	RootCommentID         int64                   `gorm:"column:root_comment_id;default:0;index" json:"root_comment_id"` // 根评论ID，用于快速查询某个根评论下的所有回复
	Level                 int32                   `gorm:"column:level;default:1" json:"level"`                           // 评论层级，根评论为1级，回复为2级及以上
	ScriptID              int64                   `gorm:"column:script_id;index" json:"script_id"`                       // 剧本ID
	DubbingID             int64                   `gorm:"column:dubbing_id;index" json:"dubbing_id"`                     // 配音ID
	UserID                int64                   `gorm:"column:user_id;not null;index" json:"user_id"`                  // 用户ID
	RepliedUserID         int64                   `gorm:"column:replied_user_id;default:0" json:"replied_user_id"`       // 被回复用户ID，用于@功能显示
	CharacterID           int64                   `gorm:"column:character_id" json:"character_id"`                       // 角色ID
	CharacterAssetID      int64                   `gorm:"column:character_asset_id" json:"character_asset_id"`           // 角色资源ID
	Content               string                  `gorm:"column:content;type:text" json:"content"`                       // 评论内容
	ContentType           svcscript.ContentType   `gorm:"column:content_type;default:1" json:"content_type"`             // 评论内容类型
	VoiceURL              string                  `gorm:"column:voice_url" json:"voice_url"`                             // 语音地址
	VoiceDuration         int32                   `gorm:"column:voice_duration" json:"voice_duration"`                   // 语音时长
	SvcVoiceUrl           string                  `gorm:"column:svc_voice_url" json:"svc_voice_url"`                     // 服务端变声地址
	SvcVoiceDuration      int32                   `gorm:"column:svc_voice_duration" json:"svc_voice_duration"`           // 服务端变声时长
	OriginalVoiceURL      string                  `gorm:"column:original_voice_url" json:"original_voice_url"`           // 原声语音地址
	OriginalVoiceDuration int32                   `gorm:"column:original_voice_duration" json:"original_voice_duration"` // 原声语音时长
	ASRText               string                  `gorm:"column:asr_text;type:text" json:"asr_text"`                     // 语音转写文本
	ASRStatus             common.ASRStatus        `gorm:"column:asr_status;default:0" json:"asr_status"`                 // 识别状态
	IsTop                 bool                    `gorm:"column:is_top;default:0" json:"is_top"`                         // 是否置顶
	IsHot                 bool                    `gorm:"column:is_hot;default:0" json:"is_hot"`                         // 是否热门
	IsAuthor              bool                    `gorm:"column:is_author;default:0" json:"is_author"`                   // 是否作者
	Status                svcscript.CommentStatus `gorm:"column:status;default:1" json:"status"`                         // 评论状态
	ReviewStatus          svcscript.ReviewStatus  `gorm:"column:review_status;default:0" json:"review_status"`           // 审核状态 1:待审核 2:机审通过 3:机审拒绝 4:人审通过 5:人审拒绝
	Likes                 int32                   `gorm:"column:likes" json:"likes"`                                     // 点赞数
	CreatedAt             int64                   `gorm:"column:created_at;index" json:"created_at"`                     // 创建时间
	UpdatedAt             int64                   `gorm:"column:updated_at;index" json:"updated_at"`                     // 更新时间
}

// TableName 表名
func (Comment) TableName() string {
	return "comments"
}

func (c Comment) GetFullVoiceURL() string {
	return alioss.FillAudioUrl(c.VoiceURL)
}

func (c Comment) GetFullOriginalVoiceURL() string {
	return alioss.FillAudioUrl(c.OriginalVoiceURL)
}

// CommentModel 评论模型
type CommentModel struct {
	BaseModelInterface
}

// NewCommentModel 创建评论模型实例
func NewCommentModel(baseModel BaseModelInterface) CommentModelInterface {
	return &CommentModel{
		baseModel,
	}
}

// buildReviewStatusCondition 构建审核状态查询条件
// 根据配置决定是否允许用户查看自己未审核通过的内容（包括审核中、审核被拒绝等所有非已审核通过状态的内容）
func (m *CommentModel) buildReviewStatusCondition(userID int64) (string, []interface{}) {
	if config.IsShowOwnUnApprovedContentEnabled() {
		// 允许用户查看自己未审核通过的内容
		return "(review_status = ? OR user_id = ?)", []interface{}{svcscript.ReviewStatus_REVIEW_STATUS_MANUAL_APPROVED, userID}
	} else {
		// 不允许用户查看自己未审核通过的内容，只能查看已审核通过的内容
		return "review_status = ?", []interface{}{svcscript.ReviewStatus_REVIEW_STATUS_MANUAL_APPROVED}
	}
}

// CreateComment 创建评论
func (m *CommentModel) CreateComment(ctx context.Context, comment *Comment) (int64, error) {
	if comment.CreatedAt == 0 {
		comment.CreatedAt = util.NowTimeMillis()
	}

	// 设置根评论ID、被回复用户ID和层级
	if comment.ParentID > 0 {
		// 获取父评论信息
		parentComment, err := m.GetCommentByID(ctx, comment.ParentID)
		if err != nil {
			return 0, err
		}

		// 设置被回复用户ID
		comment.RepliedUserID = parentComment.UserID

		// 设置根评论ID
		if parentComment.RootCommentID > 0 {
			// 父评论本身就是回复，使用父评论的根评论ID
			comment.RootCommentID = parentComment.RootCommentID
		} else {
			// 父评论是根评论，使用父评论ID作为根评论ID
			comment.RootCommentID = parentComment.ID
		}

		// 设置层级：父评论层级+1
		comment.Level = parentComment.Level + 1
	} else {
		// 根评论，root_comment_id为0，replied_user_id为0，level为1
		comment.RootCommentID = 0
		comment.RepliedUserID = 0
		comment.Level = 1
	}

	err := m.GetDB().WithContext(ctx).Create(comment).Error
	if err != nil {
		return 0, errcode.ErrCommentCreateFailed
	}

	return comment.ID, nil
}

// GetCommentByID 获取评论
func (m *CommentModel) GetCommentByID(ctx context.Context, id int64) (*Comment, error) {
	var comment Comment

	err := m.GetDB().WithContext(ctx).
		Where("id = ? AND status = ?", id, svcscript.CommentStatus_COMMENT_STATUS_NORMAL).
		First(&comment).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errcode.ErrCommentNotFound
		}
		return nil, errcode.ErrCommentQueryFailed
	}

	return &comment, nil
}

// GetComments 获取评论列表
func (m *CommentModel) GetComments(ctx context.Context, page, pageSize int32, commentType svcscript.CommentType, targetID, parentID, userID int64) ([]*Comment, int64, error) {
	var comments []*Comment
	var total int64

	// 构建审核状态查询条件
	reviewCondition, reviewArgs := m.buildReviewStatusCondition(userID)

	query := m.GetDB().WithContext(ctx).Model(&Comment{}).
		Where("status = ? AND "+reviewCondition, append([]interface{}{svcscript.CommentStatus_COMMENT_STATUS_NORMAL}, reviewArgs...)...)

	if commentType > 0 {
		query = query.Where("comment_type = ?", commentType)
	}

	if parentID > 0 {
		query = query.Where("parent_id = ?", parentID)
	} else {
		query = query.Where("parent_id = 0") // 只查询顶级评论
	}

	if targetID > 0 {
		switch commentType {
		case svcscript.CommentType_COMMENT_TYPE_SCRIPT:
			query = query.Where("script_id = ?", targetID)
		case svcscript.CommentType_COMMENT_TYPE_DUBBING:
			query = query.Where("dubbing_id = ?", targetID)
		}
	}

	// 计算总数
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, errcode.ErrCommentQueryFailed
	}

	if parentID > 0 {
		query.Order("created_at ASC") //二级评论仅时间排序
	} else {
		query.Order("is_top DESC, is_hot DESC, created_at DESC") // 一级评论按置顶、热门、时间排序
	}

	err = query.Offset(int((page - 1) * pageSize)).
		Limit(int(pageSize)).
		Find(&comments).Error
	if err != nil {
		return nil, 0, errcode.ErrCommentQueryFailed
	}

	return comments, total, nil
}

// GetCommentsByIDs 根据ID列表获取评论
func (m *CommentModel) GetCommentsByIDs(ctx context.Context, userID int64, ids []int64) ([]*Comment, error) {
	if len(ids) == 0 {
		return []*Comment{}, nil
	}

	// 构建审核状态查询条件
	reviewCondition, reviewArgs := m.buildReviewStatusCondition(userID)

	var comments []*Comment
	err := m.GetDB().WithContext(ctx).
		Where("id IN (?) AND status = ? AND "+reviewCondition, append([]interface{}{ids, svcscript.CommentStatus_COMMENT_STATUS_NORMAL}, reviewArgs...)...).
		Find(&comments).Error
	if err != nil {
		return nil, errcode.ErrCommentQueryFailed
	}

	return comments, nil
}

// UpdateComment 更新评论
func (m *CommentModel) UpdateComment(ctx context.Context, comment *Comment) error {
	err := m.GetDB().WithContext(ctx).Model(&Comment{}).Where("id = ?", comment.ID).Updates(comment).Error
	if err != nil {
		return errcode.ErrCommentUpdateFailed
	}

	return nil
}

// DeleteComment 删除评论
func (m *CommentModel) DeleteComment(ctx context.Context, id int64) error {
	// 软删除
	err := m.GetDB().WithContext(ctx).Model(&Comment{}).Where("id = ?", id).Updates(map[string]interface{}{
		"status": svcscript.CommentStatus_COMMENT_STATUS_DELETED,
	}).Error
	if err != nil {
		return errcode.ErrCommentDeleteFailed
	}

	// 递归删除子评论
	err = m.GetDB().WithContext(ctx).Model(&Comment{}).Where("parent_id = ?", id).Updates(map[string]interface{}{
		"status": svcscript.CommentStatus_COMMENT_STATUS_DELETED,
	}).Error
	if err != nil {
		return errcode.ErrCommentDeleteFailed
	}

	return nil
}

// SetCommentTop 设置评论置顶状态
func (m *CommentModel) SetCommentTop(ctx context.Context, id int64, isTop bool) error {
	updates := map[string]interface{}{
		"is_top": isTop,
	}

	err := m.GetDB().WithContext(ctx).Model(&Comment{}).Where("id = ?", id).Updates(updates).Error
	if err != nil {
		return errcode.ErrCommentUpdateFailed
	}

	return nil
}

// SetCommentTopWithExclusive 设置评论置顶状态（同一剧本下只能有一个置顶评论）
func (m *CommentModel) SetCommentTopWithExclusive(ctx context.Context, commentID int64, scriptID int64, isTop bool) error {
	return m.GetDB().WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		if isTop {
			// 如果要置顶，先取消该剧本下所有其他评论的置顶状态
			err := tx.Model(&Comment{}).
				Where("script_id = ? AND id != ? AND is_top = ? AND parent_id = 0", scriptID, commentID, true).
				Update("is_top", false).Error
			if err != nil {
				return errcode.ErrCommentUpdateFailed
			}
		}

		// 设置当前评论的置顶状态
		err := tx.Model(&Comment{}).Where("id = ?", commentID).Update("is_top", isTop).Error
		if err != nil {
			return errcode.ErrCommentUpdateFailed
		}

		return nil
	})
}

// BatchGetCommentCountByDubbingIDs 批量获取配音评论数量
func (m *CommentModel) BatchGetCommentCountByDubbingIDs(ctx context.Context, userID int64, dubbingIDs []int64) (map[int64]int32, error) {
	if len(dubbingIDs) == 0 {
		return make(map[int64]int32), nil
	}

	type Result struct {
		DubbingID int64
		Count     int64
	}
	var results []Result

	// 构建审核状态查询条件
	reviewCondition, reviewArgs := m.buildReviewStatusCondition(userID)

	err := m.GetDB().WithContext(ctx).Model(&Comment{}).
		Select("dubbing_id, COUNT(*) as count").
		Where("dubbing_id IN (?) AND comment_type = ? AND status = ? AND "+reviewCondition,
			append([]interface{}{dubbingIDs, svcscript.CommentType_COMMENT_TYPE_DUBBING, svcscript.CommentStatus_COMMENT_STATUS_NORMAL}, reviewArgs...)...).
		Group("dubbing_id").
		Find(&results).Error
	if err != nil {
		return nil, errcode.ErrCommentQueryFailed
	}

	counts := make(map[int64]int32)
	for _, result := range results {
		counts[result.DubbingID] = int32(result.Count)
	}

	return counts, nil
}

// BatchCountRepliesByParentIDs 批量获取多个父评论的回复数量
func (m *CommentModel) BatchCountRepliesByParentIDs(ctx context.Context, userID int64, parentIDs []int64) (map[int64]int64, error) {
	if len(parentIDs) == 0 {
		return make(map[int64]int64), nil
	}

	type Result struct {
		ParentID int64
		Count    int64
	}

	var results []Result

	// 构建审核状态查询条件
	reviewCondition, reviewArgs := m.buildReviewStatusCondition(userID)

	// 优化：使用root_comment_id查询，提升性能
	err := m.GetDB().WithContext(ctx).Model(&Comment{}).
		Select("root_comment_id as parent_id, COUNT(*) as count").
		Where("root_comment_id IN (?) AND status = ? AND "+reviewCondition,
			append([]interface{}{parentIDs, svcscript.CommentStatus_COMMENT_STATUS_NORMAL}, reviewArgs...)...).
		Group("root_comment_id").
		Scan(&results).Error

	if err != nil {
		return nil, errcode.ErrCommentQueryFailed
	}

	// 构建结果映射
	countMap := make(map[int64]int64, len(parentIDs))

	// 初始化所有ID的计数为0
	for _, id := range parentIDs {
		countMap[id] = 0
	}

	// 填充查询结果
	for _, result := range results {
		countMap[result.ParentID] = result.Count
	}

	return countMap, nil
}

// IncreaseLikes 增加评论点赞数
func (m *CommentModel) IncreaseLikes(ctx context.Context, id int64, count int64) error {
	if count <= 0 {
		count = 1 // 默认增加1个点赞
	}

	var updateExpr interface{}
	if count < 0 {
		updateExpr = gorm.Expr("GREATEST(likes + ?, 0)", count)
	} else {
		updateExpr = gorm.Expr("likes + ?", count)
	}

	err := m.GetDB().WithContext(ctx).Model(&Comment{}).
		Where("id = ? AND status = ?", id, svcscript.CommentStatus_COMMENT_STATUS_NORMAL).
		Update("likes", updateExpr).Error
	if err != nil {
		return errcode.ErrCommentUpdateFailed
	}

	// 更新点赞数后检查是否达到热门标准
	err = m.checkAndUpdateHotStatus(ctx, id)
	if err != nil {
		return err
	}

	return nil
}

// CheckAndUpdateHotStatus 检查并更新评论热门状态
func (m *CommentModel) checkAndUpdateHotStatus(ctx context.Context, id int64) error {
	// 获取评论详情
	comment, err := m.GetCommentByID(ctx, id)
	if err != nil {
		return err
	}

	// 检查点赞数是否达到热门标准
	isHot := comment.IsHot
	hotNum := config.GetHotCommentLikeNum()
	if !isHot && comment.Likes > hotNum {
		isHot = true
	} else if isHot && comment.Likes <= hotNum {
		isHot = false
	}

	// 更新is_hot
	if isHot != comment.IsHot {
		err = m.GetDB().WithContext(ctx).Model(&Comment{}).
			Where("id = ? AND status = ?", id, svcscript.CommentStatus_COMMENT_STATUS_NORMAL).
			Update("is_hot", isHot).Error
		if err != nil {
			return errcode.ErrCommentUpdateFailed
		}
	}

	return nil
}

// GetCommentsByRootID 根据根评论ID获取所有回复
func (m *CommentModel) GetCommentsByRootID(ctx context.Context, rootCommentID, userID int64, limit int32) ([]*Comment, error) {
	var comments []*Comment

	// 构建审核状态查询条件
	reviewCondition, reviewArgs := m.buildReviewStatusCondition(userID)

	query := m.GetDB().WithContext(ctx).
		Where("root_comment_id = ? AND status = ? AND "+reviewCondition,
			append([]interface{}{rootCommentID, svcscript.CommentStatus_COMMENT_STATUS_NORMAL}, reviewArgs...)...).
		Order("created_at DESC") // 回复按时间倒序排列

	if limit > 0 {
		query = query.Limit(int(limit))
	}

	err := query.Find(&comments).Error
	if err != nil {
		return nil, errcode.ErrCommentQueryFailed
	}

	return comments, nil
}

// GetCommentsByRootIDWithPagination 根据根评论ID获取所有回复（支持分页）
func (m *CommentModel) GetCommentsByRootIDWithPagination(ctx context.Context, rootCommentID, userID int64, page, pageSize int32) ([]*Comment, int64, error) {
	var comments []*Comment
	var total int64

	// 构建审核状态查询条件
	reviewCondition, reviewArgs := m.buildReviewStatusCondition(userID)

	baseQuery := m.GetDB().WithContext(ctx).Model(&Comment{}).
		Where("root_comment_id = ? AND status = ? AND "+reviewCondition,
			append([]interface{}{rootCommentID, svcscript.CommentStatus_COMMENT_STATUS_NORMAL}, reviewArgs...)...)

	// 计算总数
	err := baseQuery.Count(&total).Error
	if err != nil {
		return nil, 0, errcode.ErrCommentQueryFailed
	}

	// 分页查询
	err = baseQuery.
		Order("created_at ASC").
		Offset(int((page - 1) * pageSize)).
		Limit(int(pageSize)).
		Find(&comments).Error
	if err != nil {
		return nil, 0, errcode.ErrCommentQueryFailed
	}

	return comments, total, nil
}

// BatchGetRepliedUserIDs 批量获取评论的被回复用户ID
func (m *CommentModel) BatchGetRepliedUserIDs(ctx context.Context, commentIDs []int64) (map[int64]int64, error) {
	if len(commentIDs) == 0 {
		return make(map[int64]int64), nil
	}

	type Result struct {
		ID            int64
		RepliedUserID int64
	}

	var results []Result
	err := m.GetDB().WithContext(ctx).Model(&Comment{}).
		Select("id, replied_user_id").
		Where("id IN (?) AND replied_user_id > 0", commentIDs).
		Find(&results).Error

	if err != nil {
		return nil, errcode.ErrCommentQueryFailed
	}

	resultMap := make(map[int64]int64)
	for _, result := range results {
		resultMap[result.ID] = result.RepliedUserID
	}

	return resultMap, nil
}

// BatchGetRepliesByParentIDs 批量获取多个父评论的回复（一次查询）
func (m *CommentModel) BatchGetRepliesByParentIDs(ctx context.Context, parentIDs []int64, userID int64, limit int32) ([]*Comment, error) {
	if len(parentIDs) == 0 {
		return []*Comment{}, nil
	}

	var allReplies []*Comment

	// 构建审核状态查询条件
	reviewCondition, reviewArgs := m.buildReviewStatusCondition(userID)

	// 使用root_comment_id一次性查询所有回复
	err := m.GetDB().WithContext(ctx).
		Where("root_comment_id IN (?) AND status = ? AND "+reviewCondition,
			append([]interface{}{parentIDs, svcscript.CommentStatus_COMMENT_STATUS_NORMAL}, reviewArgs...)...).
		Order("root_comment_id ASC, created_at ASC"). // 按父评论分组，时间倒序
		Find(&allReplies).Error

	if err != nil {
		return nil, errcode.ErrCommentQueryFailed
	}

	// 如果有限制，手动过滤每个父评论的前N条
	if limit > 0 {
		filteredReplies := make([]*Comment, 0)
		parentCounts := make(map[int64]int32)

		for _, reply := range allReplies {
			parentID := reply.RootCommentID
			if parentID == 0 {
				parentID = reply.ParentID // 兼容旧数据
			}

			if parentCounts[parentID] < limit {
				filteredReplies = append(filteredReplies, reply)
				parentCounts[parentID]++
			}
		}

		return filteredReplies, nil
	}

	return allReplies, nil
}
