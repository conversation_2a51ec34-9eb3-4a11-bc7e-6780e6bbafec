package model

import (
	"context"
	"errors"
	"fmt"

	"new-gitlab.xunlei.cn/vcproject/backends/baselib/alioss"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/util"

	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcscript"

	"gorm.io/gorm"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/errcode"
	"new-gitlab.xunlei.cn/vcproject/backends/svcscript/config"
)

// DubbingModelInterface 配音模型接口
type DubbingModelInterface interface {
	CreateDubbingTx(ctx context.Context, tx *gorm.DB, dubbing *Dubbing) error
	GetDubbingByID(ctx context.Context, id int64) (*Dubbing, error)
	GetAllLineDubbings(ctx context.Context, userID, lineID int64) ([]*Dubbing, error)
	GetDubbingsByIDs(ctx context.Context, ids []int64) ([]*Dubbing, error)
	UpdateDubbing(ctx context.Context, dubbing *Dubbing) error
	UpdateDubbingTx(ctx context.Context, tx *gorm.DB, dubbing *Dubbing) error
	UpdateDubbingByMapTx(ctx context.Context, tx *gorm.DB, id int64, updates map[string]interface{}) error
	DeleteDubbingTx(ctx context.Context, tx *gorm.DB, id int64) error
	BatchDeleteDubbingsTx(ctx context.Context, tx *gorm.DB, ids []int64) error
	IncreaseLikes(ctx context.Context, id int64, count int64) error
	GetLineDubbingsWithCustomOrder(ctx context.Context, userID, lineID int64, completedLineIDs []int64, page, pageSize int32) ([]*Dubbing, int64, error)
	GetLineDubbingsWithSingleQuery(ctx context.Context, userID, lineID int64, completedUserIDs []int64, page, pageSize int32) ([]*Dubbing, int64, error)
	GetLineDubbingsForSimpleDisplay(ctx context.Context, userID, lineID int64, completedLineIDs []int64, limit int32) ([]*Dubbing, error)
	BatchGetLineDubbingsForSimple(ctx context.Context, lineIDs []int64, userID int64) (map[int64][]*Dubbing, error)
	GetUserScriptGroupedDubbings(ctx context.Context, userID int64, page, pageSize int32) (map[int64][]*Dubbing, []int64, int64, error)
	GetDubbingByUserAndLineID(ctx context.Context, userID, lineID int64) (*Dubbing, error)
	SetDubbingTopWithExclusive(ctx context.Context, dubbingID int64, lineID int64, isTop bool) error
}

// Dubbing 配音表结构
type Dubbing struct {
	ID               int64                  `gorm:"primaryKey;column:id" json:"id"`                               // 配音ID
	ScriptID         int64                  `gorm:"column:script_id;not null;index" json:"script_id"`             // 剧本ID
	LineID           int64                  `gorm:"column:line_id;not null;index:idx_script_line" json:"line_id"` // 台词ID
	UserID           int64                  `gorm:"column:user_id;not null;index" json:"user_id"`                 // 用户ID
	CharacterID      int64                  `gorm:"column:character_id" json:"character_id"`                      // 角色ID
	CharacterAssetID int64                  `gorm:"column:character_asset_id" json:"character_asset_id"`          // 角色资源ID
	OriginalURL      string                 `gorm:"column:original_url" json:"original_url"`                      // 原声地址
	OriginalDuration int32                  `gorm:"column:original_duration;default:0" json:"original_duration"`  // 原声时长
	DubbedURL        string                 `gorm:"column:dubbed_url;not null" json:"dubbed_url"`                 // 配音地址
	DubbedDuration   int32                  `gorm:"column:dubbed_duration;default:0" json:"dubbed_duration"`      // 配音时长
	IsAuthor         bool                   `gorm:"column:is_author;default:0" json:"is_author"`                  // 是否作者配音
	IsTop            bool                   `gorm:"column:is_top;default:0" json:"is_top"`                        // 是否置顶
	Likes            int32                  `gorm:"column:likes;default:0" json:"likes"`                          // 点赞数
	Status           svcscript.Status       `gorm:"column:status;default:1" json:"status"`                        // 状态 1:正常 2:删除
	ReviewStatus     svcscript.ReviewStatus `gorm:"column:review_status;default:0" json:"review_status"`          // 审核状态 1:待审核 2:机审通过 3:机审拒绝 4:人审通过 5:人审拒绝
	CreatedAt        int64                  `gorm:"column:created_at" json:"created_at"`                          // 创建时间
	UpdatedAt        int64                  `gorm:"column:updated_at" json:"updated_at"`                          // 更新时间
}

// TableName 表名
func (Dubbing) TableName() string {
	return "dubbings"
}

func (v Dubbing) GetFullOriginalURL() string {
	return alioss.FillAudioUrl(v.OriginalURL)
}

func (v Dubbing) GetFullDubbedURL() string {
	return alioss.FillAudioUrl(v.DubbedURL)
}

// DubbingModel 配音模型
type DubbingModel struct {
	BaseModelInterface
}

// NewDubbingModel 创建配音模型实例
func NewDubbingModel(baseModel BaseModelInterface) DubbingModelInterface {
	return &DubbingModel{
		baseModel,
	}
}

// buildReviewStatusCondition 构建审核状态查询条件
// 根据配置决定是否允许用户查看自己未审核通过的内容（包括审核中、审核被拒绝等所有非已审核通过状态的内容）
func (m *DubbingModel) buildReviewStatusCondition(userID int64) (string, []interface{}) {
	if config.IsShowOwnUnApprovedContentEnabled() {
		// 允许用户查看自己未审核通过的内容
		return "(review_status = ? OR user_id = ?)", []interface{}{svcscript.ReviewStatus_REVIEW_STATUS_MANUAL_APPROVED, userID}
	} else {
		// 不允许用户查看自己未审核通过的内容，只能查看已审核通过的内容
		return "review_status = ?", []interface{}{svcscript.ReviewStatus_REVIEW_STATUS_MANUAL_APPROVED}
	}
}

// CreateDubbing 创建配音
func (m *DubbingModel) CreateDubbingTx(ctx context.Context, tx *gorm.DB, dubbing *Dubbing) error {
	now := util.NowTimeMillis()
	if dubbing.CreatedAt == 0 {
		dubbing.CreatedAt = now
	}
	if dubbing.UpdatedAt == 0 {
		dubbing.UpdatedAt = now
	}

	err := tx.WithContext(ctx).Create(dubbing).Error
	if err != nil {
		return errcode.ErrDubbingCreateFailed
	}

	return nil
}

// GetDubbingByID 获取配音
func (m *DubbingModel) GetDubbingByID(ctx context.Context, id int64) (*Dubbing, error) {
	var dubbing Dubbing

	err := m.GetDB().WithContext(ctx).
		Where("id = ? AND status = ?", id, svcscript.ScriptStatus_SCRIPT_STATUS_NORMAL).
		First(&dubbing).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errcode.ErrDubbingNotFound
		}
		return nil, errcode.ErrDubbingQueryFailed
	}

	return &dubbing, nil
}

// GetDubbingsByIDs 批量获取配音
func (m *DubbingModel) GetDubbingsByIDs(ctx context.Context, ids []int64) ([]*Dubbing, error) {
	if len(ids) == 0 {
		return []*Dubbing{}, nil
	}

	var dubbings []*Dubbing
	err := m.GetDB().WithContext(ctx).
		Where("id IN ? AND status = ?", ids, svcscript.Status_STATUS_ACTIVE).
		Find(&dubbings).Error
	if err != nil {
		return nil, errcode.ErrDubbingQueryFailed
	}

	return dubbings, nil
}

// UpdateDubbing 更新配音
func (m *DubbingModel) UpdateDubbing(ctx context.Context, dubbing *Dubbing) error {
	dubbing.UpdatedAt = util.NowTimeMillis()

	err := m.GetDB().WithContext(ctx).Model(&Dubbing{}).Where("id = ?", dubbing.ID).Updates(dubbing).Error
	if err != nil {
		return errcode.ErrDubbingUpdateFailed
	}

	return nil
}

// DeleteDubbing 删除配音
func (m *DubbingModel) DeleteDubbingTx(ctx context.Context, tx *gorm.DB, id int64) error {
	// 软删除，只更新状态
	updates := map[string]interface{}{
		"status":     svcscript.ScriptStatus_SCRIPT_STATUS_DELETED,
		"updated_at": util.NowTimeMillis(),
	}

	err := tx.WithContext(ctx).Model(&Dubbing{}).Where("id = ?", id).Updates(updates).Error
	if err != nil {
		return errcode.ErrDubbingDeleteFailed
	}

	return nil
}

// BatchDeleteDubbingsTx 批量删除配音（优化版本，单条SQL执行）
func (m *DubbingModel) BatchDeleteDubbingsTx(ctx context.Context, tx *gorm.DB, ids []int64) error {
	if len(ids) == 0 {
		return nil
	}

	// 软删除，只更新状态 - 使用单条SQL批量更新，避免循环
	updates := map[string]interface{}{
		"status":     svcscript.ScriptStatus_SCRIPT_STATUS_DELETED,
		"updated_at": util.NowTimeMillis(),
	}

	err := tx.WithContext(ctx).Model(&Dubbing{}).
		Where("id IN ? AND status = ?", ids, svcscript.Status_STATUS_ACTIVE).
		Updates(updates).Error
	if err != nil {
		return errcode.ErrDubbingDeleteFailed
	}

	return nil
}

// IncreaseLikes 增加配音点赞数
func (m *DubbingModel) IncreaseLikes(ctx context.Context, id int64, count int64) error {
	if count <= 0 {
		count = 1 // 默认增加1个点赞
	}

	var updateExpr interface{}
	if count < 0 {
		updateExpr = gorm.Expr("GREATEST(likes + ?, 0)", count)
	} else {
		updateExpr = gorm.Expr("likes + ?", count)
	}

	err := m.GetDB().WithContext(ctx).Model(&Dubbing{}).
		Where("id = ? AND status = ?", id, svcscript.ScriptStatus_SCRIPT_STATUS_NORMAL).
		Update("likes", updateExpr).Error
	if err != nil {
		return errcode.ErrDubbingUpdateFailed
	}

	return nil
}

// GetLineDubbingsWithCustomOrder 获取台词的所有配音，使用新的排序规则
// 排序规则：当前用户的配音 > 置顶配音 > 已完整完成当前台词角色配音的用户的配音 > 点赞数高的配音
// 每个分组内部按创建时间倒序排序
func (m *DubbingModel) GetLineDubbingsWithCustomOrder(ctx context.Context, userID, lineID int64, completedCharacterIDs []int64, page, pageSize int32) ([]*Dubbing, int64, error) {
	var total int64

	// 构建审核状态查询条件
	reviewCondition, reviewArgs := m.buildReviewStatusCondition(userID)

	// 计算总数
	err := m.GetDB().WithContext(ctx).Model(&Dubbing{}).
		Where("line_id = ? AND status = ? AND "+reviewCondition,
			append([]interface{}{lineID, svcscript.ScriptStatus_SCRIPT_STATUS_NORMAL}, reviewArgs...)...).
		Count(&total).Error
	if err != nil {
		return nil, 0, errcode.ErrDubbingQueryFailed
	}

	if total == 0 {
		return []*Dubbing{}, 0, nil
	}

	// 先查询当前用户的配音
	var userDubbings []*Dubbing
	err = m.GetDB().WithContext(ctx).
		Where("line_id = ? AND user_id = ? AND status = ?",
			lineID, userID, svcscript.ScriptStatus_SCRIPT_STATUS_NORMAL).
		Order("created_at DESC").
		Find(&userDubbings).Error
	if err != nil {
		return nil, 0, errcode.ErrDubbingQueryFailed
	}

	// 查询所有置顶的配音（除了用户自己的）
	var toppedDubbings []*Dubbing
	err = m.GetDB().WithContext(ctx).
		Where("line_id = ? AND user_id != ? AND is_top = ? AND status = ? AND review_status = ?",
			lineID, userID, true, svcscript.ScriptStatus_SCRIPT_STATUS_NORMAL, svcscript.ReviewStatus_REVIEW_STATUS_MANUAL_APPROVED).
		Order("created_at DESC").
		Find(&toppedDubbings).Error
	if err != nil {
		return nil, 0, errcode.ErrDubbingQueryFailed
	}

	// 查询已完整完成当前台词角色配音的用户的配音（排除掉已经在前两组的配音）
	var completedDubbings []*Dubbing
	if len(completedCharacterIDs) > 0 {
		var excludeUserIDs []int64
		for _, vo := range userDubbings {
			excludeUserIDs = append(excludeUserIDs, vo.UserID)
		}
		for _, vo := range toppedDubbings {
			excludeUserIDs = append(excludeUserIDs, vo.UserID)
		}

		query := m.GetDB().WithContext(ctx).
			Where("line_id = ? AND status = ? AND review_status = ?",
				lineID, svcscript.ScriptStatus_SCRIPT_STATUS_NORMAL, svcscript.ReviewStatus_REVIEW_STATUS_MANUAL_APPROVED)

		if len(excludeUserIDs) > 0 {
			query = query.Where("user_id NOT IN ?", excludeUserIDs)
		}

		// 使用IN查询查找已完成角色配音的用户的配音
		query = query.Where("user_id IN (SELECT user_id FROM dubbings WHERE line_id = ? AND character_id IN ? AND status = ? AND review_status = ? GROUP BY user_id)",
			lineID, completedCharacterIDs, svcscript.ScriptStatus_SCRIPT_STATUS_NORMAL, svcscript.ReviewStatus_REVIEW_STATUS_MANUAL_APPROVED)

		err = query.Order("created_at DESC").
			Find(&completedDubbings).Error
		if err != nil {
			return nil, 0, errcode.ErrDubbingQueryFailed
		}
	}

	// 查询剩余的配音（排除掉已经在前三组的配音）
	var otherDubbings []*Dubbing
	var excludeUserIDs []int64
	for _, vo := range userDubbings {
		excludeUserIDs = append(excludeUserIDs, vo.UserID)
	}
	for _, vo := range toppedDubbings {
		excludeUserIDs = append(excludeUserIDs, vo.UserID)
	}
	for _, vo := range completedDubbings {
		excludeUserIDs = append(excludeUserIDs, vo.UserID)
	}

	query := m.GetDB().WithContext(ctx).
		Where("line_id = ? AND status = ? AND review_status = ?",
			lineID, svcscript.ScriptStatus_SCRIPT_STATUS_NORMAL, svcscript.ReviewStatus_REVIEW_STATUS_MANUAL_APPROVED)

	if len(excludeUserIDs) > 0 {
		query = query.Where("user_id NOT IN ?", excludeUserIDs)
	}

	err = query.Order("likes DESC, created_at DESC").
		Find(&otherDubbings).Error
	if err != nil {
		return nil, 0, errcode.ErrDubbingQueryFailed
	}

	// 合并所有结果
	combinedResults := make([]*Dubbing, 0, len(userDubbings)+len(toppedDubbings)+len(completedDubbings)+len(otherDubbings))
	combinedResults = append(combinedResults, userDubbings...)
	combinedResults = append(combinedResults, toppedDubbings...)
	combinedResults = append(combinedResults, completedDubbings...)
	combinedResults = append(combinedResults, otherDubbings...)

	// 根据页码计算应该返回的结果范围
	startIndex := int((page - 1) * pageSize)
	endIndex := int(page * pageSize)

	if startIndex >= len(combinedResults) {
		// 超出范围，返回空结果
		return []*Dubbing{}, total, nil
	}

	if endIndex > len(combinedResults) {
		endIndex = len(combinedResults)
	}

	return combinedResults[startIndex:endIndex], total, nil
}

// GetLineDubbingsWithSingleQuery 使用单查询获取台词配音，优化版本
// 注意：这个方法目前有逻辑问题，第三优先级的逻辑与原有逻辑不匹配
// 原逻辑：查找"在当前台词中配音过已完成角色的用户"
// 当前逻辑：查找"已完成角色配音的用户"
// 建议暂时不使用此方法，直到修复完成
func (m *DubbingModel) GetLineDubbingsWithSingleQuery(ctx context.Context, userID, lineID int64, completedUserIDs []int64, page, pageSize int32) ([]*Dubbing, int64, error) {
	var total int64

	// 构建审核状态查询条件
	reviewCondition, reviewArgs := m.buildReviewStatusCondition(userID)

	// 1. 计算总数
	err := m.GetDB().WithContext(ctx).Model(&Dubbing{}).
		Where("line_id = ? AND status = ? AND "+reviewCondition,
			append([]interface{}{lineID, svcscript.ScriptStatus_SCRIPT_STATUS_NORMAL}, reviewArgs...)...).
		Count(&total).Error
	if err != nil {
		return nil, 0, errcode.ErrDubbingQueryFailed
	}

	if total == 0 {
		return []*Dubbing{}, 0, nil
	}

	// 2. 构建单个复杂查询
	// 注意：这里的逻辑与原有逻辑不完全匹配，需要修复
	query := m.GetDB().WithContext(ctx).Model(&Dubbing{}).
		Select("*, CASE "+
			"WHEN user_id = ? THEN 1 "+
			"WHEN is_top = 1 AND user_id != ? THEN 2 "+
			"WHEN user_id IN (?) THEN 3 "+
			"ELSE 4 "+
			"END as priority_order, "+
			"CASE "+
			"WHEN user_id = ? OR is_top = 1 THEN created_at "+
			"ELSE likes "+
			"END as sort_value",
			userID, userID, completedUserIDs, userID).
		Where("line_id = ? AND status = ? AND "+reviewCondition,
			append([]interface{}{lineID, svcscript.ScriptStatus_SCRIPT_STATUS_NORMAL}, reviewArgs...)...).
		Order("priority_order ASC, sort_value DESC, created_at DESC").
		Offset(int((page - 1) * pageSize)).
		Limit(int(pageSize))

	var dubbings []*Dubbing
	err = query.Find(&dubbings).Error
	if err != nil {
		return nil, 0, errcode.ErrDubbingQueryFailed
	}

	return dubbings, total, nil
}

// GetLineDubbingsForSimpleDisplay 获取台词的配音简略显示列表，仅显示排序后的固定数量配音，按用户ID去重
func (m *DubbingModel) GetLineDubbingsForSimpleDisplay(ctx context.Context, userID, lineID int64, completedCharacterIDs []int64, limit int32) ([]*Dubbing, error) {
	// 先查询当前用户的配音
	var userDubbings []*Dubbing
	err := m.GetDB().WithContext(ctx).
		Where("line_id = ? AND user_id = ? AND status = ?",
			lineID, userID, svcscript.ScriptStatus_SCRIPT_STATUS_NORMAL).
		Order("created_at DESC").
		Limit(1). // 只取用户最新的一条配音
		Find(&userDubbings).Error
	if err != nil {
		return nil, errcode.ErrDubbingQueryFailed
	}
	// 用于首页展示台词首条配音查询
	if len(userDubbings) == 1 && limit == 1 {
		return userDubbings, nil
	}

	// 查询所有置顶的配音（除了用户自己的）
	var toppedDubbings []*Dubbing
	err = m.GetDB().WithContext(ctx).
		Where("line_id = ? AND user_id != ? AND is_top = ? AND status = ? AND review_status = ?",
			lineID, userID, true, svcscript.ScriptStatus_SCRIPT_STATUS_NORMAL, svcscript.ReviewStatus_REVIEW_STATUS_MANUAL_APPROVED).
		Order("created_at DESC").
		Find(&toppedDubbings).Error
	if err != nil {
		return nil, errcode.ErrDubbingQueryFailed
	}
	// 用于首页展示台词首条配音查询
	if len(userDubbings) == 0 && len(toppedDubbings) > 0 && limit == 1 {
		return toppedDubbings[:1], nil
	}

	// 查询已完整完成当前台词角色配音的用户的配音（排除掉已经在前两组的配音）
	var completedDubbings []*Dubbing
	if len(completedCharacterIDs) > 0 {
		var excludeUserIDs []int64
		for _, vo := range userDubbings {
			excludeUserIDs = append(excludeUserIDs, vo.UserID)
		}
		for _, vo := range toppedDubbings {
			excludeUserIDs = append(excludeUserIDs, vo.UserID)
		}

		query := m.GetDB().WithContext(ctx).
			Where("line_id = ? AND status = ? AND review_status = ?",
				lineID, svcscript.ScriptStatus_SCRIPT_STATUS_NORMAL, svcscript.ReviewStatus_REVIEW_STATUS_MANUAL_APPROVED)

		if len(excludeUserIDs) > 0 {
			query = query.Where("user_id NOT IN ?", excludeUserIDs)
		}

		// 使用IN查询查找已完成角色配音的用户的配音
		query = query.Where("user_id IN (SELECT user_id FROM dubbings WHERE line_id = ? AND character_id IN ? AND status = ? AND review_status = ? GROUP BY user_id)",
			lineID, completedCharacterIDs, svcscript.ScriptStatus_SCRIPT_STATUS_NORMAL, svcscript.ReviewStatus_REVIEW_STATUS_MANUAL_APPROVED)

		err = query.Order("likes DESC, created_at DESC").
			Find(&completedDubbings).Error
		if err != nil {
			return nil, errcode.ErrDubbingQueryFailed
		}
	}

	// 查询剩余的配音（排除掉已经在前三组的配音）
	var otherDubbings []*Dubbing
	var excludeUserIDs []int64
	for _, vo := range userDubbings {
		excludeUserIDs = append(excludeUserIDs, vo.UserID)
	}
	for _, vo := range toppedDubbings {
		excludeUserIDs = append(excludeUserIDs, vo.UserID)
	}
	for _, vo := range completedDubbings {
		excludeUserIDs = append(excludeUserIDs, vo.UserID)
	}

	query := m.GetDB().WithContext(ctx).
		Where("line_id = ? AND status = ? AND review_status = ?",
			lineID, svcscript.ScriptStatus_SCRIPT_STATUS_NORMAL, svcscript.ReviewStatus_REVIEW_STATUS_MANUAL_APPROVED)

	if len(excludeUserIDs) > 0 {
		query = query.Where("user_id NOT IN ?", excludeUserIDs)
	}

	err = query.Order("likes DESC, created_at DESC").
		Find(&otherDubbings).Error
	if err != nil {
		return nil, errcode.ErrDubbingQueryFailed
	}

	// 合并所有结果
	combinedResults := make([]*Dubbing, 0, len(userDubbings)+len(toppedDubbings)+len(completedDubbings)+len(otherDubbings))
	combinedResults = append(combinedResults, userDubbings...)
	combinedResults = append(combinedResults, toppedDubbings...)

	// 对于已完成配音和其他配音，需要按用户ID去重
	seenUserIDs := make(map[int64]bool)
	for _, vo := range userDubbings {
		seenUserIDs[vo.UserID] = true
	}
	for _, vo := range toppedDubbings {
		seenUserIDs[vo.UserID] = true
	}

	// 添加已完成的配音（去重）
	for _, vo := range completedDubbings {
		if !seenUserIDs[vo.UserID] {
			combinedResults = append(combinedResults, vo)
			seenUserIDs[vo.UserID] = true
			// 检查是否已达到限制
			if int32(len(combinedResults)) >= limit {
				return combinedResults[:limit], nil
			}
		}
	}

	// 添加其他配音（去重）
	for _, vo := range otherDubbings {
		if !seenUserIDs[vo.UserID] {
			combinedResults = append(combinedResults, vo)
			seenUserIDs[vo.UserID] = true
			// 检查是否已达到限制
			if int32(len(combinedResults)) >= limit {
				return combinedResults[:limit], nil
			}
		}
	}

	return combinedResults, nil
}

// GetAllLineDubbings 获取台词的所有配音数据（不分页）
func (m *DubbingModel) GetAllLineDubbings(ctx context.Context, userID, lineID int64) ([]*Dubbing, error) {
	var dubbings []*Dubbing

	// 构建审核状态查询条件
	reviewCondition, reviewArgs := m.buildReviewStatusCondition(userID)

	err := m.GetDB().WithContext(ctx).
		Where("line_id = ? AND status = ? AND "+reviewCondition,
			append([]interface{}{lineID, svcscript.ScriptStatus_SCRIPT_STATUS_NORMAL}, reviewArgs...)...).
		Order("created_at DESC").
		Find(&dubbings).Error

	if err != nil {
		return nil, errcode.ErrDubbingQueryFailed
	}

	return dubbings, nil
}

// GetUserScriptGroupedDubbings 获取用户按剧本分组的配音数据（按最后配音时间排序）
func (m *DubbingModel) GetUserScriptGroupedDubbings(ctx context.Context, userID int64, page, pageSize int32) (map[int64][]*Dubbing, []int64, int64, error) {
	var scriptIDs []int64
	var total int64

	// 先查询用户所有配音过的剧本数量（计算总数）
	err := m.GetDB().WithContext(ctx).Model(&Dubbing{}).
		Select("DISTINCT script_id").
		Where("user_id = ? AND status = ?", userID, svcscript.ScriptStatus_SCRIPT_STATUS_NORMAL).
		Count(&total).Error
	if err != nil {
		return nil, nil, 0, errcode.ErrDubbingQueryFailed
	}

	// 获取分页后的剧本IDs
	type ScriptLastDubbing struct {
		ScriptID     int64 `gorm:"column:script_id"`
		LastDubbedAt int64 `gorm:"column:last_dubbed_at"`
	}
	var scriptLastDubbings []ScriptLastDubbing

	err = m.GetDB().WithContext(ctx).Model(&Dubbing{}).
		Select("script_id, MAX(created_at) as last_dubbed_at").
		Where("user_id = ? AND status = ?", userID, svcscript.ScriptStatus_SCRIPT_STATUS_NORMAL).
		Group("script_id").
		Order("last_dubbed_at DESC").
		Offset(int((page - 1) * pageSize)).
		Limit(int(pageSize)).
		Scan(&scriptLastDubbings).Error
	if err != nil {
		return nil, nil, 0, errcode.ErrDubbingQueryFailed
	}

	for _, item := range scriptLastDubbings {
		scriptIDs = append(scriptIDs, item.ScriptID)
	}

	if len(scriptIDs) == 0 {
		return map[int64][]*Dubbing{}, scriptIDs, total, nil
	}

	// 获取这些剧本的所有配音记录
	var dubbings []*Dubbing
	err = m.GetDB().WithContext(ctx).
		Where("user_id = ? AND script_id IN ? AND status = ?", userID, scriptIDs, svcscript.ScriptStatus_SCRIPT_STATUS_NORMAL).
		Order("created_at DESC").
		Find(&dubbings).Error
	if err != nil {
		return nil, nil, 0, errcode.ErrDubbingQueryFailed
	}

	// 按剧本ID分组
	result := make(map[int64][]*Dubbing)
	for _, dubbing := range dubbings {
		result[dubbing.ScriptID] = append(result[dubbing.ScriptID], dubbing)
	}

	// 返回按剧本ID分组的配音记录和排序后的剧本ID列表（便于保持排序）
	return result, scriptIDs, total, nil
}

// GetDubbingByUserAndLineID 根据用户ID和台词ID获取配音
func (m *DubbingModel) GetDubbingByUserAndLineID(ctx context.Context, userID, lineID int64) (*Dubbing, error) {
	var dubbing Dubbing

	err := m.GetDB().WithContext(ctx).
		Where("user_id = ? AND line_id = ? AND status = ?",
			userID, lineID, svcscript.ScriptStatus_SCRIPT_STATUS_NORMAL).
		First(&dubbing).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil // 返回nil表示没有找到记录
		}
		return nil, errcode.ErrDubbingQueryFailed
	}

	return &dubbing, nil
}

// UpdateDubbingTx 在事务中更新配音
func (m *DubbingModel) UpdateDubbingTx(ctx context.Context, tx *gorm.DB, dubbing *Dubbing) error {
	dubbing.UpdatedAt = util.NowTimeMillis()

	err := tx.WithContext(ctx).Model(&Dubbing{}).
		Where("id = ?", dubbing.ID).
		Updates(dubbing).Error
	if err != nil {
		return errcode.ErrDubbingUpdateFailed
	}

	return nil
}

func (m *DubbingModel) UpdateDubbingByMapTx(ctx context.Context, tx *gorm.DB, id int64, updates map[string]interface{}) error {
	updates["updated_at"] = util.NowTimeMillis()

	err := tx.WithContext(ctx).Model(&Dubbing{}).
		Where("id = ?", id).
		Updates(updates).Error
	if err != nil {
		return errcode.ErrDubbingUpdateFailed
	}

	return nil
}

// BatchGetLineDubbingsForSimple 批量获取台词的简略配音数据
func (m *DubbingModel) BatchGetLineDubbingsForSimple(ctx context.Context, lineIDs []int64, userID int64) (map[int64][]*Dubbing, error) {
	if len(lineIDs) == 0 {
		return make(map[int64][]*Dubbing), nil
	}

	// 批量获取所有台词的配音
	// 排序规则：先按台词ID分组，然后按照原有逻辑的复合排序
	// 1. 用户配音按 created_at DESC
	// 2. 置顶配音按 created_at DESC
	// 3. 其他配音按 likes DESC, created_at DESC
	var allDubbings []*Dubbing

	// 构建审核状态查询条件
	reviewCondition, reviewArgs := m.buildReviewStatusCondition(userID)

	// 构建复合排序的 SQL
	orderSQL := fmt.Sprintf("line_id, CASE WHEN user_id = %d THEN 1 WHEN is_top = 1 THEN 2 ELSE 3 END, CASE WHEN user_id = %d OR is_top = 1 THEN created_at ELSE likes END DESC, created_at DESC", userID, userID)

	err := m.GetDB().WithContext(ctx).
		Where("line_id IN ? AND status = ? AND "+reviewCondition,
			append([]interface{}{lineIDs, svcscript.ScriptStatus_SCRIPT_STATUS_NORMAL}, reviewArgs...)...).
		Order(orderSQL).
		Find(&allDubbings).Error

	if err != nil {
		return nil, err
	}

	// 按台词分组
	result := make(map[int64][]*Dubbing)
	for _, dubbing := range allDubbings {
		result[dubbing.LineID] = append(result[dubbing.LineID], dubbing)
	}

	return result, nil
}

// SetDubbingTopWithExclusive 设置配音置顶状态（同一台词下只能有一个置顶配音）
func (m *DubbingModel) SetDubbingTopWithExclusive(ctx context.Context, dubbingID int64, lineID int64, isTop bool) error {
	return m.GetDB().WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		if isTop {
			// 如果要置顶，先取消该台词下所有其他配音的置顶状态
			err := tx.Model(&Dubbing{}).
				Where("line_id = ? AND id != ? AND is_top = ? AND status = ?", lineID, dubbingID, true, svcscript.Status_STATUS_ACTIVE).
				Update("is_top", false).Error
			if err != nil {
				return errcode.ErrDubbingUpdateFailed
			}
		}

		// 设置当前配音的置顶状态
		err := tx.Model(&Dubbing{}).
			Where("id = ? AND status = ?", dubbingID, svcscript.Status_STATUS_ACTIVE).
			Update("is_top", isTop).Error
		if err != nil {
			return errcode.ErrDubbingUpdateFailed
		}

		return nil
	})
}
