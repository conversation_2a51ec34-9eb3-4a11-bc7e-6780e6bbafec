package model

import (
	"context"
	"errors"
	"gorm.io/gorm"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/errcode"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/util"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcscript"
	"strings"
)

// LineModelInterface 台词模型接口
type LineModelInterface interface {
	GetLineByID(ctx context.Context, id int64) (*Line, error)
	GetLinesByScriptID(ctx context.Context, scriptID int64) ([]*Line, error)
	UpdateLine(ctx context.Context, line *Line) error
	BatchGetLinesByScriptIDs(ctx context.Context, scriptIDs []int64) (map[int64][]*Line, error)
	BatchCreateLinesTx(ctx context.Context, tx interface{}, lines []*Line) error
	IncreaseVoiceCountTx(ctx context.Context, tx *gorm.DB, id int64, count int64) error
	BatchIncreaseVoiceCountTx(ctx context.Context, tx *gorm.DB, lineCountMap map[int64]int64) error
}

// Line 台词表结构
type Line struct {
	ID               int64  `gorm:"primaryKey;column:id" json:"id"`                      // 台词ID
	ScriptID         int64  `gorm:"column:script_id;not null;index" json:"script_id"`    // 剧本ID
	Content          string `gorm:"column:content;not null;type:text" json:"content"`    // 台词内容
	Sort             int32  `gorm:"column:sort;default:0" json:"sort"`                   // 排序值
	CharacterID      int64  `gorm:"column:character_id" json:"character_id"`             // 角色ID
	CharacterAssetID int64  `gorm:"column:character_asset_id" json:"character_asset_id"` // 角色资源ID
	BackgroundUrl    string `gorm:"column:background_url" json:"background_url"`         // 台词背景
	DubbingCount     int32  `gorm:"column:dubbing_count" json:"dubbing_count"`           // 配音数
	DubbingDuration  int32  `gorm:"column:dubbing_duration" json:"dubbing_duration"`     // 配音时长
	Status           int32  `gorm:"column:status" json:"status"`                         // 状态 1：正常 2：删除
	CreatedAt        int64  `gorm:"column:created_at" json:"created_at"`                 // 创建时间
	UpdatedAt        int64  `gorm:"column:updated_at" json:"updated_at"`                 // 更新时间
}

// TableName 表名
func (Line) TableName() string {
	return "lines"
}

// LineModel 台词模型
type LineModel struct {
	BaseModelInterface
}

// NewLineModel 创建台词模型实例
func NewLineModel(baseModel BaseModelInterface) LineModelInterface {
	return &LineModel{
		baseModel,
	}
}

// GetLineByID 获取台词
func (m *LineModel) GetLineByID(ctx context.Context, id int64) (*Line, error) {
	var line Line

	err := m.GetDB().WithContext(ctx).Where("id = ?", id).First(&line).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errcode.ErrLineNotFound
		}
		return nil, errcode.ErrLineQueryFailed
	}

	return &line, nil
}

// GetLinesByScriptID 获取剧本的所有台词
func (m *LineModel) GetLinesByScriptID(ctx context.Context, scriptID int64) ([]*Line, error) {
	var lines []*Line

	err := m.GetDB().WithContext(ctx).
		Where("script_id = ? AND status = ?", scriptID, svcscript.Status_STATUS_ACTIVE).
		Order("sort ASC").
		Find(&lines).Error
	if err != nil {
		return nil, errcode.ErrLineQueryFailed
	}

	return lines, nil
}

// UpdateLine 更新台词
func (m *LineModel) UpdateLine(ctx context.Context, line *Line) error {
	line.UpdatedAt = util.NowTimeMillis()

	err := m.GetDB().WithContext(ctx).Model(&Line{}).Where("id = ?", line.ID).Updates(line).Error
	if err != nil {
		return errcode.ErrLineUpdateFailed
	}

	return nil
}

// BatchGetLinesByScriptIDs 批量获取多个剧本的台词
func (m *LineModel) BatchGetLinesByScriptIDs(ctx context.Context, scriptIDs []int64) (map[int64][]*Line, error) {
	if len(scriptIDs) == 0 {
		return map[int64][]*Line{}, nil
	}

	var lines []*Line
	err := m.GetDB().WithContext(ctx).
		Where("script_id IN (?)", scriptIDs).
		Order("script_id, sort ASC, id ASC").
		Find(&lines).Error
	if err != nil {
		return nil, errcode.ErrLineQueryFailed
	}

	// 按剧本ID分组
	result := make(map[int64][]*Line)
	for _, line := range lines {
		result[line.ScriptID] = append(result[line.ScriptID], line)
	}

	return result, nil
}

// BatchCreateLinesTx 在事务中批量创建台词
func (m *LineModel) BatchCreateLinesTx(ctx context.Context, tx interface{}, lines []*Line) error {
	if len(lines) == 0 {
		return nil
	}

	now := util.NowTimeMillis()
	for i := range lines {
		if lines[i].CreatedAt == 0 {
			lines[i].CreatedAt = now
		}
		if lines[i].UpdatedAt == 0 {
			lines[i].UpdatedAt = now
		}
	}

	err := tx.(*gorm.DB).Create(lines).Error
	if err != nil {
		return errcode.ErrLineCreateFailed
	}

	return nil
}

// IncreaseVoiceCount 增加/减少台词配音数
func (m *LineModel) IncreaseVoiceCountTx(ctx context.Context, tx *gorm.DB, id int64, count int64) error {
	var updateExpr interface{}
	if count < 0 {
		updateExpr = gorm.Expr("GREATEST(dubbing_count + ?, 0)", count)
	} else {
		updateExpr = gorm.Expr("dubbing_count + ?", count)
	}

	err := tx.WithContext(ctx).Model(&Line{}).
		Where("id = ?", id).
		Update("dubbing_count", updateExpr).Error
	if err != nil {
		return errcode.ErrDubbingUpdateFailed
	}

	return nil
}

// BatchIncreaseVoiceCountTx 批量增加/减少台词配音数
func (m *LineModel) BatchIncreaseVoiceCountTx(ctx context.Context, tx *gorm.DB, lineCountMap map[int64]int64) error {
	if len(lineCountMap) == 0 {
		return nil
	}

	var lineIDs []int64
	var args []interface{}
	var caseWhenParts []string

	for lineID, count := range lineCountMap {
		lineIDs = append(lineIDs, lineID)
		caseWhenParts = append(caseWhenParts, "WHEN ? THEN ?")
		args = append(args, lineID, count)
	}

	// 使用 GREATEST 函数确保结果不会小于 0
	sql := `UPDATE ` + "`lines`" + `
			SET dubbing_count = GREATEST(dubbing_count + (
				CASE id ` + strings.Join(caseWhenParts, " ") + ` ELSE 0 END
			), 0)
			WHERE id IN (` + strings.Repeat("?,", len(lineIDs)-1) + "?)"

	for _, lineID := range lineIDs {
		args = append(args, lineID)
	}

	err := tx.WithContext(ctx).Exec(sql, args...).Error
	if err != nil {
		return errcode.ErrDubbingUpdateFailed
	}

	return nil
}
