package model

import (
	"context"
	"errors"
	"gorm.io/gorm"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/alioss"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/errcode"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/util"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcscript"
)

// TopicModelInterface 话题模型接口
type TopicModelInterface interface {
	CreateTopic(ctx context.Context, topic *Topic) error
	GetTopics(ctx context.Context, page, pageSize int32, scene svcscript.TOPIC_SCENE) ([]*Topic, int64, error)
	SearchTopics(ctx context.Context, keyword string, page, pageSize int32) ([]*Topic, int64, error)
	GetTopicByName(ctx context.Context, name string) (*Topic, error)
	GetTopicLabelsByIDs(ctx context.Context, ids []int64) (map[int64]*TopicLabel, error)
	GetTopicsByNames(ctx context.Context, names []string) ([]*Topic, error)
	UpdateTopicStatus(ctx context.Context, id int64, status svcscript.ScriptStatus) error
	BatchUpdateTopicStatus(ctx context.Context, ids []int64, status svcscript.ScriptStatus) error

	// 统计数据
	IncreaseScript(ctx context.Context, id int64, count int64) error
	IncreaseDubbing(ctx context.Context, id int64, count int64) error
	IncreaseView(ctx context.Context, id int64, count int64) error
}

// Topic 话题表结构
type Topic struct {
	ID            int64                  `gorm:"primaryKey;column:id" json:"id"`                        // 话题ID
	Name          string                 `gorm:"column:name;not null;uniqueIndex" json:"name"`          // 话题名称
	LabelID       int64                  `gorm:"column:label_id" json:"label_id"`                       // 标签ID`
	Sort          int32                  `gorm:"column:sort;default:0" json:"sort"`                     // 排序值
	IsRecommend   bool                   `gorm:"column:is_recommend;default:0" json:"is_recommend"`     // 是否推荐(首页tab）
	RecommendSort int32                  `gorm:"column:recommend_sort;default:0" json:"recommend_sort"` // 推荐排序
	IsHot         bool                   `gorm:"column:is_hot;default:0" json:"is_hot"`                 // 是否热门
	HotSort       int32                  `gorm:"column:hot_sort;default:0" json:"hot_sort"`             // 热门排序
	ScriptCount   int32                  `gorm:"column:script_count" json:"script_count"`               // 剧本数
	DubbingCount  int32                  `gorm:"column:dubbing_count" json:"dubbing_count"`             // 配音数
	ViewCount     int32                  `gorm:"column:view_count" json:"view_count"`                   // 浏览量
	CreatedAt     int64                  `gorm:"column:created_at" json:"created_at"`                   // 创建时间
	UpdatedAt     int64                  `gorm:"column:updated_at" json:"updated_at"`                   // 更新时间
	Status        svcscript.ScriptStatus `gorm:"column:status;default:1" json:"status"`                 // 状态
}

// TableName 表名
func (Topic) TableName() string {
	return "topics"
}

type TopicLabel struct {
	ID    int64  `gorm:"primaryKey;column:id" json:"id"`                  // 标签ID
	Name  string `gorm:"column:name;size:20;unique;not null" json:"name"` // 标签名，比如：热、新、爆
	Sort  int    `gorm:"column:sort;default:0" json:"sort"`               // 标签排序
	Color string `gorm:"column:color;size:10;default:''" json:"color"`    // 可选：标签颜色或样式标识
	BgURL string `gorm:"column:bg_url;size:125;default:''" json:"bg_url"` // 标签背景图
}

func (m TopicLabel) FullBgUrl() string {
	return alioss.FillImageUrl(m.BgURL)
}

// TableName 表名
func (m TopicLabel) TableName() string {
	return "topic_labels"
}

// TopicModel 话题模型
type TopicModel struct {
	BaseModelInterface
}

// NewTopicModel 创建话题模型实例
func NewTopicModel(baseModel BaseModelInterface) TopicModelInterface {
	return &TopicModel{
		baseModel,
	}
}

// CreateTopic 创建话题
func (m *TopicModel) CreateTopic(ctx context.Context, topic *Topic) error {
	now := util.NowTimeMillis()
	if topic.CreatedAt == 0 {
		topic.CreatedAt = now
	}
	if topic.UpdatedAt == 0 {
		topic.UpdatedAt = now
	}

	err := m.GetDB().WithContext(ctx).Create(topic).Error
	if err != nil {
		return errcode.ErrTopicCreateFailed
	}

	return nil
}

func (m *TopicModel) GetTopicLabelsByIDs(ctx context.Context, ids []int64) (map[int64]*TopicLabel, error) {
	if len(ids) == 0 {
		return nil, nil
	}

	var labels []*TopicLabel
	err := m.GetDB().WithContext(ctx).Where("id IN (?) AND status = 1", ids).Find(&labels).Error
	if err != nil {
		return nil, errcode.ErrTopicQueryFailed
	}

	var result = make(map[int64]*TopicLabel, len(labels))
	for _, label := range labels {
		result[label.ID] = label
	}

	return result, nil
}

// GetTopics 获取话题列表
func (m *TopicModel) GetTopics(ctx context.Context, page, pageSize int32, scene svcscript.TOPIC_SCENE) ([]*Topic, int64, error) {
	var topics []*Topic
	var total int64

	query := m.GetDB().WithContext(ctx).Model(&Topic{}).Where("status = 1")

	if scene == svcscript.TOPIC_SCENE_INDEX_TAB {
		query = query.Where("is_recommend = ?", true)
	} else if scene == svcscript.TOPIC_SCENE_HOT {
		query = query.Where("is_hot = ?", true)
	}

	// 计算总数
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, errcode.ErrTopicQueryFailed
	}

	// 构建排序规则
	orderClause := "sort DESC, script_count DESC, created_at DESC"
	if scene == svcscript.TOPIC_SCENE_INDEX_TAB {
		orderClause = "recommend_sort DESC, sort DESC, created_at DESC"
	} else if scene == svcscript.TOPIC_SCENE_HOT {
		orderClause = "hot_sort DESC, sort DESC, created_at DESC"
	}

	// 查询分页数据
	err = query.
		Order(orderClause).
		Offset(int((page - 1) * pageSize)).
		Limit(int(pageSize)).
		Find(&topics).Error
	if err != nil {
		return nil, 0, errcode.ErrTopicQueryFailed
	}

	return topics, total, nil
}

// SearchTopics 搜索话题
func (m *TopicModel) SearchTopics(ctx context.Context, keyword string, page, pageSize int32) ([]*Topic, int64, error) {
	var topics []*Topic
	var total int64

	likeTerm := "%" + keyword + "%"

	// 计算总数，使用BINARY确保精确匹配emoji字符
	err := m.GetDB().WithContext(ctx).Model(&Topic{}).
		Where("name LIKE BINARY ? AND status = 1", likeTerm).
		Count(&total).Error
	if err != nil {
		return nil, 0, errcode.ErrTopicQueryFailed
	}

	// 查询分页数据
	err = m.GetDB().WithContext(ctx).
		Where("name LIKE BINARY ? AND status = 1", likeTerm).
		Order("script_count DESC, created_at DESC").
		Offset(int((page - 1) * pageSize)).
		Limit(int(pageSize)).
		Find(&topics).Error
	if err != nil {
		return nil, 0, errcode.ErrTopicQueryFailed
	}

	return topics, total, nil
}

// GetTopicByName 根据名称获取话题
func (m *TopicModel) GetTopicByName(ctx context.Context, name string) (*Topic, error) {
	var topic Topic

	err := m.GetDB().WithContext(ctx).Where("name = ? AND status = 1", name).First(&topic).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errcode.ErrTopicNotFound
		}
		return nil, errcode.ErrTopicQueryFailed
	}

	return &topic, nil
}

// IncreaseScript 增加话题剧本数
func (m *TopicModel) IncreaseScript(ctx context.Context, id int64, count int64) error {
	var updateExpr interface{}
	if count < 0 {
		updateExpr = gorm.Expr("GREATEST(script_count + ?, 0)", count)
	} else {
		updateExpr = gorm.Expr("script_count + ?", count)
	}

	err := m.GetDB().WithContext(ctx).Model(&Topic{}).
		Where("id = ?", id).
		Update("script_count", updateExpr).Error
	if err != nil {
		return errcode.ErrScriptUpdateFailed
	}

	return nil
}

// IncreaseDubbing 增加话题配音数
func (m *TopicModel) IncreaseDubbing(ctx context.Context, id int64, count int64) error {
	var updateExpr interface{}
	if count < 0 {
		updateExpr = gorm.Expr("GREATEST(dubbing_count + ?, 0)", count)
	} else {
		updateExpr = gorm.Expr("dubbing_count + ?", count)
	}

	err := m.GetDB().WithContext(ctx).Model(&Topic{}).
		Where("id = ?", id).
		Update("dubbing_count", updateExpr).Error
	if err != nil {
		return errcode.ErrScriptUpdateFailed
	}

	return nil
}

// IncreaseView 增加话题浏览数
func (m *TopicModel) IncreaseView(ctx context.Context, id int64, count int64) error {
	var updateExpr interface{}
	if count < 0 {
		updateExpr = gorm.Expr("GREATEST(view_count + ?, 0)", count)
	} else {
		updateExpr = gorm.Expr("view_count + ?", count)
	}

	err := m.GetDB().WithContext(ctx).Model(&Topic{}).
		Where("id = ?", id).
		Update("view_count", updateExpr).Error
	if err != nil {
		return errcode.ErrScriptUpdateFailed
	}
	return nil
}

// GetTopicsByNames 根据话题名称列表获取话题
func (m *TopicModel) GetTopicsByNames(ctx context.Context, names []string) ([]*Topic, error) {
	if len(names) == 0 {
		return []*Topic{}, nil
	}

	var topics []*Topic
	err := m.GetDB().WithContext(ctx).Where("name IN (?)", names).Find(&topics).Error
	if err != nil {
		return nil, errcode.ErrTopicQueryFailed
	}

	return topics, nil
}

// UpdateTopicStatus 更新话题状态
func (m *TopicModel) UpdateTopicStatus(ctx context.Context, id int64, status svcscript.ScriptStatus) error {
	updates := map[string]interface{}{
		"status":     status,
		"updated_at": util.NowTimeMillis(),
	}

	err := m.GetDB().WithContext(ctx).Model(&Topic{}).Where("id = ?", id).Updates(updates).Error
	if err != nil {
		return errcode.ErrTopicUpdateFailed
	}

	return nil
}

// BatchUpdateTopicStatus 批量更新话题状态
func (m *TopicModel) BatchUpdateTopicStatus(ctx context.Context, ids []int64, status svcscript.ScriptStatus) error {
	if len(ids) == 0 {
		return nil
	}

	updates := map[string]interface{}{
		"status":     status,
		"updated_at": util.NowTimeMillis(),
	}

	err := m.GetDB().WithContext(ctx).Model(&Topic{}).Where("id IN (?)", ids).Updates(updates).Error
	if err != nil {
		return errcode.ErrTopicUpdateFailed
	}

	return nil
}
