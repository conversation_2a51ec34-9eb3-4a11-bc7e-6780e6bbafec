package model

import (
	"context"
	"errors"
	"fmt"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/alioss"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/util"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcscript"

	"gorm.io/gorm"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
)

// Character 角色模型
type Character struct {
	ID            int64            `gorm:"primaryKey;column:id" json:"id"`                        // 角色ID
	Name          string           `gorm:"column:name;not null" json:"name"`                      // 角色名称
	ModelPth      string           `gorm:"column:model_pth" json:"model_pth"`                     // 角色模型pth
	ModelIdx      string           `gorm:"column:model_idx" json:"model_idx"`                     // 角色模型index
	Sort          int32            `gorm:"column:sort;default:0" json:"sort"`                     // 角色排序
	IPID          int64            `gorm:"column:ip_id" json:"ip_id"`                             // 角色所属IP
	IPName        string           `gorm:"column:ip_name" json:"ip_name"`                         // IP名称
	DubbingCount  int64            `gorm:"column:dubbing_count;default:0" json:"dubbing_count"`   // 角色配音次数
	PreviewAudio  string           `gorm:"column:preview_audio" json:"preview_audio"`             // 试听音频
	IsRecommended bool             `gorm:"column:is_recommended;default:0" json:"is_recommended"` // 是否推荐
	Status        svcscript.Status `gorm:"column:status;default:1" json:"status"`                 // 状态（1:正常，2:删除）
	CreatedAt     int64            `gorm:"column:created_at" json:"created_at"`                   // 创建时间
	UpdatedAt     int64            `gorm:"column:updated_at" json:"updated_at"`                   // 更新时间
}

// TableName 表名
func (*Character) TableName() string {
	return "characters"
}

func (c *Character) GetFullPreviewAudio() string {
	if c.PreviewAudio == "" {
		return ""
	}
	return alioss.FillImageUrl(c.PreviewAudio)
}

// CharacterModelInterface 角色模型接口
type CharacterModelInterface interface {
	// 查询方法
	GetCharacters(ctx context.Context, page, pageSize int32, ipID int64) ([]*Character, int64, error)
	GetRecommendedCharacters(ctx context.Context, page, pageSize int32) ([]*Character, int64, error)
	GetCharacterByID(ctx context.Context, id int64) (*Character, error)
	GetCharactersByIDs(ctx context.Context, ids []int64) ([]*Character, error)
	SearchCharacters(ctx context.Context, keyword string, page, pageSize int32) ([]*Character, int64, error)
	// 更新方法
	IncrementDubbings(ctx context.Context, id int64, count int32) error
	// 批量获取方法
	BatchGetCharactersByIDs(ctx context.Context, ids []int64) (map[int64]*Character, error)
	// 批量检查角色可用性（根据状态判断）
	BatchCheckCharacterAvailability(ctx context.Context, ids []int64) (map[int64]bool, error)
	// 获取角色总数
	GetCharacterCount(ctx context.Context) (int64, error)

	// 角色素材/资源包相关方法
	GetCharactersWithAssetsByCharIds(ctx context.Context, ids []int64) (map[int64]*Character, map[int64][]*CharacterAsset, error)
	GetCharactersWithAsset(ctx context.Context, ids []int64, assetIds []int64) (map[int64]*Character, map[int64]*CharacterAsset, error)
}

// CharacterModel 角色模型实现
type CharacterModel struct {
	BaseModelInterface
	assetModel CharacterAssetModelInterface
}

// NewCharacterModel 创建角色模型实例
func NewCharacterModel(baseModel BaseModelInterface, assetModel CharacterAssetModelInterface) CharacterModelInterface {
	return &CharacterModel{
		baseModel,
		assetModel,
	}
}

// GetCharacters 获取角色列表
func (m *CharacterModel) GetCharacters(ctx context.Context, page, pageSize int32, ipID int64) ([]*Character, int64, error) {
	var characters []*Character
	var count int64

	query := m.GetDB().WithContext(ctx).Model(&Character{}).Where("status = ?", 1)
	if ipID > 0 {
		query = query.Where("ip_id = ?", ipID)
	}

	// 获取总数
	if err := query.Count(&count).Error; err != nil {
		logger.Errorf("GetCharacters count error: %v", err)
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	if err := query.Order("sort DESC, id DESC").Offset(int(offset)).Limit(int(pageSize)).Find(&characters).Error; err != nil {
		logger.Errorf("GetCharacters find error: %v", err)
		return nil, 0, err
	}

	return characters, count, nil
}

// GetRecommendedCharacters 获取推荐角色列表
func (m *CharacterModel) GetRecommendedCharacters(ctx context.Context, page, pageSize int32) ([]*Character, int64, error) {
	var characters []*Character
	var count int64

	query := m.GetDB().WithContext(ctx).Model(&Character{}).Where("status = ? AND is_recommended = ?", 1, true)

	// 获取总数
	if err := query.Count(&count).Error; err != nil {
		logger.Errorf("GetRecommendedCharacters count error: %v", err)
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	if err := query.Order("sort DESC, id DESC").Offset(int(offset)).Limit(int(pageSize)).Find(&characters).Error; err != nil {
		logger.Errorf("GetRecommendedCharacters find error: %v", err)
		return nil, 0, err
	}

	return characters, count, nil
}

// GetCharacterByID 根据ID获取角色
func (m *CharacterModel) GetCharacterByID(ctx context.Context, id int64) (*Character, error) {
	var character Character

	if err := m.GetDB().WithContext(ctx).Model(&Character{}).Where("id = ? AND status = ?", id, 1).First(&character).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		logger.Errorf("GetCharacterByID error: %v", err)
		return nil, err
	}

	return &character, nil
}

// GetCharactersByIDs 根据多个ID获取角色列表
func (m *CharacterModel) GetCharactersByIDs(ctx context.Context, ids []int64) ([]*Character, error) {
	if len(ids) == 0 {
		return []*Character{}, nil
	}

	var characters []*Character

	if err := m.GetDB().WithContext(ctx).Model(&Character{}).Where("id IN (?) AND status = ?", ids, 1).Find(&characters).Error; err != nil {
		logger.Errorf("GetCharactersByIDs error: %v", err)
		return nil, err
	}

	return characters, nil
}

// SearchCharacters 搜索角色
func (m *CharacterModel) SearchCharacters(ctx context.Context, keyword string, page, pageSize int32) ([]*Character, int64, error) {
	var characters []*Character
	var count int64

	query := m.GetDB().WithContext(ctx).Model(&Character{}).Where("status = ?", 1)
	if keyword != "" {
		likeKeyword := fmt.Sprintf("%%%s%%", keyword)
		// 使用BINARY确保精确匹配emoji字符
		query = query.Where("name LIKE BINARY ? OR ip_name LIKE BINARY ?", likeKeyword, likeKeyword)
	}

	// 获取总数
	if err := query.Count(&count).Error; err != nil {
		logger.Errorf("SearchCharacters count error: %v", err)
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	if err := query.Order("sort DESC, id DESC").Offset(int(offset)).Limit(int(pageSize)).Find(&characters).Error; err != nil {
		logger.Errorf("SearchCharacters find error: %v", err)
		return nil, 0, err
	}

	return characters, count, nil
}

// IncrementDubbings 增加配音计数
func (m *CharacterModel) IncrementDubbings(ctx context.Context, id int64, count int32) error {
	var updateExpr interface{}
	if count < 0 {
		// 当count为负数时，使用GREATEST函数确保结果不小于0
		updateExpr = gorm.Expr("GREATEST(dubbing_count + ?, 0)", count)
	} else {
		// 当count为正数或0时，直接相加
		updateExpr = gorm.Expr("dubbing_count + ?", count)
	}

	if err := m.GetDB().WithContext(ctx).Model(&Character{}).Where("id = ?", id).
		UpdateColumn("dubbing_count", updateExpr).
		UpdateColumn("updated_at", util.NowTimeMillis()).Error; err != nil {
		logger.Errorf("IncrementVoiceCount error: %v", err)
		return err
	}

	return nil
}

// BatchGetCharactersByIDs 批量获取角色信息
func (m *CharacterModel) BatchGetCharactersByIDs(ctx context.Context, ids []int64) (map[int64]*Character, error) {
	if len(ids) == 0 {
		return make(map[int64]*Character), nil
	}

	// 获取角色基本信息
	characters, err := m.GetCharactersByIDs(ctx, ids)
	if err != nil {
		return nil, err
	}

	result := make(map[int64]*Character)
	for _, char := range characters {
		result[char.ID] = char
	}

	return result, nil
}

// BatchCheckCharacterAvailability 批量检查角色可用性
func (m *CharacterModel) BatchCheckCharacterAvailability(ctx context.Context, ids []int64) (map[int64]bool, error) {
	if len(ids) == 0 {
		return make(map[int64]bool), nil
	}

	// 查询角色状态
	var characters []*Character
	err := m.GetDB().WithContext(ctx).
		Select("id, status").
		Where("id IN (?)", ids).
		Find(&characters).Error

	if err != nil {
		logger.Errorf("BatchCheckCharacterAvailability error: %v", err)
		return nil, err
	}

	// 构建结果映射
	result := make(map[int64]bool, len(ids))

	// 初始化所有ID为不可用
	for _, id := range ids {
		result[id] = false
	}

	// 标记可用的角色（状态为1表示正常）
	for _, char := range characters {
		result[char.ID] = char.Status == svcscript.Status_STATUS_ACTIVE
	}

	return result, nil
}

// GetCharactersWithAssets 批量获取角色及其所有资源包
func (m *CharacterModel) GetCharactersWithAssetsByCharIds(ctx context.Context, ids []int64) (map[int64]*Character, map[int64][]*CharacterAsset, error) {
	if len(ids) == 0 {
		return make(map[int64]*Character), make(map[int64][]*CharacterAsset), nil
	}

	// 获取角色信息
	characters, err := m.GetCharactersByIDs(ctx, ids)
	if err != nil {
		return nil, nil, err
	}

	characterMap := make(map[int64]*Character)
	for _, char := range characters {
		characterMap[char.ID] = char
	}

	// 获取资源包信息
	assetMap, err := m.assetModel.GetAssetsByCharacterIDs(ctx, ids, true)
	if err != nil {
		return characterMap, nil, err
	}

	// 过滤掉没有资源包的角色
	for id := range characterMap {
		if _, ok := assetMap[id]; !ok {
			delete(characterMap, id)
		}
	}

	return characterMap, assetMap, nil
}

// GetCharactersWithAsset 获取角色及其指定资源包
func (m *CharacterModel) GetCharactersWithAsset(ctx context.Context, ids []int64, assetIds []int64) (map[int64]*Character, map[int64]*CharacterAsset, error) {
	if len(ids) == 0 || len(assetIds) == 0 {
		return make(map[int64]*Character), make(map[int64]*CharacterAsset), nil
	}

	characters, err := m.GetCharactersByIDs(ctx, ids)
	if err != nil {
		return nil, nil, err
	}

	characterMap := make(map[int64]*Character)
	for _, char := range characters {
		characterMap[char.ID] = char
	}

	assetsMap, err := m.assetModel.GetAssetsCharIdMapByIDs(ctx, assetIds)
	if err != nil {
		return nil, nil, err
	}

	return characterMap, assetsMap, nil
}

// GetCharacterCount 获取角色总数
func (m *CharacterModel) GetCharacterCount(ctx context.Context) (int64, error) {
	var count int64
	if err := m.GetDB().WithContext(ctx).Model(&Character{}).
		Where("status = ?", svcscript.Status_STATUS_ACTIVE).
		Count(&count).Error; err != nil {
		logger.Errorf("GetCharacterCount error: %v", err)
		return 0, err
	}

	return count, nil
}
