#!/bin/bash

# SMS 发送器多平台编译脚本

set -e

# 项目信息
APP_NAME="sms-sender"
VERSION=${VERSION:-"1.0.0"}
BUILD_TIME=$(date '+%Y-%m-%d %H:%M:%S')
GIT_COMMIT=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")

# 构建信息
LDFLAGS="-s -w -X 'main.Version=${VERSION}' -X 'main.BuildTime=${BUILD_TIME}' -X 'main.GitCommit=${GIT_COMMIT}'"

# 输出目录
OUTPUT_DIR="dist"
mkdir -p ${OUTPUT_DIR}

# 支持的平台
PLATFORMS=(
    "linux/amd64"
    "linux/arm64"
    "darwin/amd64"
    "darwin/arm64"
    "windows/amd64"
)

echo "开始编译 ${APP_NAME} v${VERSION}"
echo "构建时间: ${BUILD_TIME}"
echo "Git提交: ${GIT_COMMIT}"
echo ""

# 编译函数
build_platform() {
    local platform=$1
    local os=$(echo $platform | cut -d'/' -f1)
    local arch=$(echo $platform | cut -d'/' -f2)
    
    local output_name="${APP_NAME}-${os}-${arch}"
    if [ "$os" = "windows" ]; then
        output_name="${output_name}.exe"
    fi
    
    local output_path="${OUTPUT_DIR}/${output_name}"
    
    echo "编译 ${platform} -> ${output_path}"
    
    GOOS=$os GOARCH=$arch go build \
        -ldflags "${LDFLAGS}" \
        -o "${output_path}" \
        ./cmd/sms-sender
    
    if [ $? -eq 0 ]; then
        echo "✅ ${platform} 编译成功"
        
        # 显示文件信息
        if command -v file >/dev/null 2>&1; then
            file "${output_path}"
        fi
        
        # 显示文件大小
        if [ "$os" = "darwin" ]; then
            ls -lh "${output_path}" | awk '{print "   大小: " $5}'
        else
            ls -lh "${output_path}" | awk '{print "   大小: " $5}'
        fi
        echo ""
    else
        echo "❌ ${platform} 编译失败"
        return 1
    fi
}

# 检查 Go 环境
if ! command -v go >/dev/null 2>&1; then
    echo "错误: 未找到 Go 编译器"
    exit 1
fi

echo "Go 版本: $(go version)"
echo ""

# 清理旧的构建文件
if [ "$1" = "clean" ]; then
    echo "清理构建目录..."
    rm -rf ${OUTPUT_DIR}
    echo "清理完成"
    exit 0
fi

# 编译指定平台
if [ -n "$1" ]; then
    if [[ " ${PLATFORMS[@]} " =~ " $1 " ]]; then
        build_platform "$1"
    else
        echo "错误: 不支持的平台 '$1'"
        echo "支持的平台: ${PLATFORMS[@]}"
        exit 1
    fi
    exit 0
fi

# 编译所有平台
success_count=0
total_count=${#PLATFORMS[@]}

for platform in "${PLATFORMS[@]}"; do
    if build_platform "$platform"; then
        ((success_count++))
    fi
done

echo "编译完成: ${success_count}/${total_count} 个平台编译成功"
echo ""

# 显示构建结果
if [ -d "${OUTPUT_DIR}" ]; then
    echo "构建文件列表:"
    ls -lh ${OUTPUT_DIR}/
    echo ""
    
    # 创建校验和文件
    if command -v sha256sum >/dev/null 2>&1; then
        echo "生成校验和文件..."
        cd ${OUTPUT_DIR}
        sha256sum * > checksums.txt
        cd ..
        echo "校验和文件: ${OUTPUT_DIR}/checksums.txt"
    elif command -v shasum >/dev/null 2>&1; then
        echo "生成校验和文件..."
        cd ${OUTPUT_DIR}
        shasum -a 256 * > checksums.txt
        cd ..
        echo "校验和文件: ${OUTPUT_DIR}/checksums.txt"
    fi
fi

echo ""
echo "使用说明:"
echo "  Linux x64:   ${OUTPUT_DIR}/${APP_NAME}-linux-amd64"
echo "  Linux ARM64: ${OUTPUT_DIR}/${APP_NAME}-linux-arm64"
echo "  macOS x64:   ${OUTPUT_DIR}/${APP_NAME}-darwin-amd64"
echo "  macOS ARM64: ${OUTPUT_DIR}/${APP_NAME}-darwin-arm64"
echo "  Windows x64: ${OUTPUT_DIR}/${APP_NAME}-windows-amd64.exe"
echo ""
echo "快速部署到 Linux 服务器:"
echo "  scp ${OUTPUT_DIR}/${APP_NAME}-linux-amd64 user@server:/path/to/${APP_NAME}"
echo "  ssh user@server 'chmod +x /path/to/${APP_NAME} && /path/to/${APP_NAME} -h'"
