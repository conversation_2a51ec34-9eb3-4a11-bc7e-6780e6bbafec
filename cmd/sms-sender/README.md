# SMS 周期性发送器

这是一个从 `svcaccount/services/sms_test.go` 中的 `TestSendSMSPeriodic` 测试方法转换而来的独立可执行程序，用于在服务器上长时间运行，周期性发送短信。

## 功能特性

- 周期性向指定手机号码列表发送短信验证码
- 支持自定义发送间隔时间
- 支持命令行参数配置
- 优雅停止机制（支持 Ctrl+C 和 SIGTERM 信号）
- 详细的日志输出
- 每个号码之间自动间隔1秒，避免频率过高

## 编译方法

在项目根目录（`/Users/<USER>/go/src/new-gitlab.xunlei.cn/vcproject/backends`）执行：

```bash
go build -o sms-sender ./cmd/sms-sender
```

## 运行方法

### 使用默认参数运行

```bash
./sms-sender
```

默认配置：
- 发送间隔：5分钟
- 手机号码：***********,***********,***********,***********
- 短信签名：音活
- 模板代码：SMS_315720114

### 使用自定义参数运行

```bash
./sms-sender \
  -interval 10 \
  -phones "***********,***********" \
  -sign-name "自定义签名" \
  -template-code "SMS_123456789"
```

### 使用自定义阿里云配置

```bash
./sms-sender \
  -access-key-id "YOUR_ACCESS_KEY_ID" \
  -access-key-secret "YOUR_ACCESS_KEY_SECRET" \
  -endpoint "dysmsapi.aliyuncs.com" \
  -interval 3 \
  -phones "***********"
```

## 命令行参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `-access-key-id` | string | LTAI5tGQgk1kzpYb7EndyuFR | 阿里云AccessKeyId |
| `-access-key-secret` | string | ****************************** | 阿里云AccessKeySecret |
| `-endpoint` | string | dysmsapi.aliyuncs.com | 短信服务端点 |
| `-sign-name` | string | 音活 | 短信签名 |
| `-template-code` | string | SMS_315720114 | 短信模板代码 |
| `-interval` | int | 5 | 发送间隔（分钟） |
| `-phones` | string | ***********,***********,***********,*********** | 手机号码列表，用逗号分隔 |

## 运行示例

### 1. 每3分钟向单个号码发送短信

```bash
./sms-sender -interval 3 -phones "***********"
```

### 2. 每10分钟向多个号码发送短信

```bash
./sms-sender -interval 10 -phones "***********,***********,13700137000"
```

### 3. 查看帮助信息

```bash
./sms-sender -h
```

## 日志输出示例

```
2024/08/05 19:20:15 将向以下手机号码发送短信: [*********** *********** *********** ***********]
2024/08/05 19:20:15 发送间隔: 5分钟
2024/08/05 19:20:15 周期性短信发送服务启动，每5m0s执行一次
2024/08/05 19:20:15 服务将持续运行，按 Ctrl+C 手动停止
2024/08/05 19:20:15 开始批量发送短信，时间: 2024-08-05 19:20:15
2024/08/05 19:20:16 成功发送短信到 ***********, 验证码: 123456, 响应: {OK 发送成功 ...}
2024/08/05 19:20:17 成功发送短信到 ***********
2024/08/05 19:20:18 成功发送短信到 ***********
2024/08/05 19:20:19 成功发送短信到 ***********
2024/08/05 19:20:19 批量发送短信完成，时间: 2024-08-05 19:20:19
```

## 停止程序

程序支持优雅停止，可以通过以下方式停止：

1. **Ctrl+C**：在终端中按 Ctrl+C
2. **SIGTERM 信号**：`kill <进程ID>`

程序收到停止信号后会：
1. 停止定时器
2. 等待当前正在执行的发送任务完成
3. 输出停止日志
4. 安全退出

## 依赖要求

程序依赖以下 Go 模块（已在项目 go.mod 中配置）：

- `github.com/alibabacloud-go/darabonba-openapi/v2`
- `github.com/alibabacloud-go/dysmsapi-20170525/v4`
- `github.com/alibabacloud-go/tea-utils/v2`
- `github.com/alibabacloud-go/tea`

## 配置要求

1. **阿里云短信服务**：需要有效的阿里云 AccessKey 和短信服务权限
2. **短信模板**：需要在阿里云控制台配置对应的短信模板
3. **短信签名**：需要在阿里云控制台配置对应的短信签名

## 注意事项

1. 请确保阿里云账户有足够的短信余额
2. 请遵守短信发送频率限制，避免被限流
3. 生产环境建议使用环境变量或配置文件管理敏感信息（AccessKey等）
4. 建议在测试环境充分测试后再部署到生产环境

## 与原测试方法的差异

1. **移除测试框架依赖**：不再依赖 `testing.T`，使用标准 `log` 包输出日志
2. **增加命令行参数**：支持通过命令行参数配置所有选项
3. **结构化代码**：将功能封装为 `SMSSender` 结构体，代码更清晰
4. **增强日志**：添加更详细的启动和配置信息日志
5. **保持核心逻辑**：发送逻辑、定时机制、信号处理等核心功能完全保持一致
