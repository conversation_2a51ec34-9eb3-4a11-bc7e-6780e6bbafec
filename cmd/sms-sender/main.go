package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"log"
	"math/rand"
	"os"
	"os/signal"
	"strings"
	"sync"
	"syscall"
	"time"

	openapi "github.com/alibabacloud-go/darabonba-openapi/v2/client"
	dysmsapi20170525 "github.com/alibabacloud-go/dysmsapi-20170525/v4/client"
	util "github.com/alibabacloud-go/tea-utils/v2/service"
	"github.com/alibabacloud-go/tea/tea"
)

// SMSConfig 短信配置
type SMSConfig struct {
	AccessKeyId     string
	AccessKeySecret string
	Endpoint        string
	SignName        string
	TemplateCode    string
}

// SMSSender 短信发送器
type SMSSender struct {
	client       *dysmsapi20170525.Client
	config       *SMSConfig
	phoneNumbers []string
	interval     time.Duration
}

// NewSMSSender 创建短信发送器
func NewSMSSender(config *SMSConfig, phoneNumbers []string, interval time.Duration) (*SMSSender, error) {
	aliConfig := &openapi.Config{
		AccessKeyId:     tea.String(config.AccessKeyId),
		AccessKeySecret: tea.String(config.AccessKeySecret),
		Endpoint:        tea.String(config.Endpoint),
	}

	client, err := dysmsapi20170525.NewClient(aliConfig)
	if err != nil {
		return nil, fmt.Errorf("创建短信客户端失败: %v", err)
	}

	return &SMSSender{
		client:       client,
		config:       config,
		phoneNumbers: phoneNumbers,
		interval:     interval,
	}, nil
}

// sendSMS 发送单条短信
func (s *SMSSender) sendSMS(phoneNumber string) error {
	v := rand.Intn(1000000)
	code := fmt.Sprintf("%06d", v)

	templateParam := map[string]string{
		"code": code,
	}

	paramJSON, err := json.Marshal(templateParam)
	if err != nil {
		return fmt.Errorf("json marshal err: %v", err)
	}

	request := &dysmsapi20170525.SendSmsRequest{
		PhoneNumbers:  tea.String(phoneNumber),
		SignName:      tea.String(s.config.SignName),
		TemplateCode:  tea.String(s.config.TemplateCode),
		TemplateParam: tea.String(string(paramJSON)),
	}

	response, err := s.client.SendSmsWithOptions(request, &util.RuntimeOptions{})
	if err != nil {
		return fmt.Errorf("发送短信失败: %v", err)
	}

	if *response.Body.Code != "OK" {
		return fmt.Errorf("短信发送响应错误: %s, %s", *response.Body.Code, *response.Body.Message)
	}

	log.Printf("成功发送短信到 %s, 验证码: %s, 响应: %v", phoneNumber, code, *response.Body)
	return nil
}

// sendBatchSMS 批量发送短信
func (s *SMSSender) sendBatchSMS() {
	log.Printf("开始批量发送短信，时间: %s", time.Now().Format("2006-01-02 15:04:05"))

	for _, phoneNumber := range s.phoneNumbers {
		if err := s.sendSMS(phoneNumber); err != nil {
			log.Printf("发送短信到 %s 失败: %v", phoneNumber, err)
		} else {
			log.Printf("成功发送短信到 %s", phoneNumber)
		}

		// 每个号码之间间隔1秒，避免频率过高
		time.Sleep(1 * time.Second)
	}

	log.Printf("批量发送短信完成，时间: %s", time.Now().Format("2006-01-02 15:04:05"))
}

// Start 启动周期性发送服务
func (s *SMSSender) Start(ctx context.Context) {
	var wg sync.WaitGroup

	wg.Add(1)
	go func() {
		defer wg.Done()

		// 创建定时器
		ticker := time.NewTicker(s.interval)
		defer ticker.Stop()

		log.Printf("周期性短信发送服务启动，每%v执行一次", s.interval)

		// 立即执行一次
		s.sendBatchSMS()

		for {
			select {
			case <-ctx.Done():
				log.Printf("收到停止信号，周期性短信发送服务正在停止...")
				return
			case <-ticker.C:
				s.sendBatchSMS()
			}
		}
	}()

	log.Printf("服务将持续运行，按 Ctrl+C 手动停止")

	<-ctx.Done()
	log.Printf("收到停止信号，正在停止周期性发送...")

	wg.Wait()
	log.Printf("周期性短信发送服务完成")
}

func main() {
	// 命令行参数
	var (
		accessKeyId     = flag.String("access-key-id", "LTAI5tGQgk1kzpYb7EndyuFR", "阿里云AccessKeyId")
		accessKeySecret = flag.String("access-key-secret", "******************************", "阿里云AccessKeySecret")
		endpoint        = flag.String("endpoint", "dysmsapi.aliyuncs.com", "短信服务端点")
		signName        = flag.String("sign-name", "音活", "短信签名")
		templateCode    = flag.String("template-code", "SMS_315720114", "短信模板代码")
		intervalMinutes = flag.Int("interval", 5, "发送间隔（分钟）")
		phoneList       = flag.String("phones", "13418852584,15914138067,14774972450,13725519305", "手机号码列表，用逗号分隔")
	)
	flag.Parse()

	// 解析手机号码列表
	phoneNumbers := []string{}
	if *phoneList != "" {
		phones := strings.Split(*phoneList, ",")
		for _, phone := range phones {
			phone = strings.TrimSpace(phone)
			if phone != "" {
				phoneNumbers = append(phoneNumbers, phone)
			}
		}
	}

	if len(phoneNumbers) == 0 {
		log.Fatal("至少需要提供一个手机号码")
	}

	log.Printf("将向以下手机号码发送短信: %v", phoneNumbers)
	log.Printf("发送间隔: %d分钟", *intervalMinutes)

	// 创建配置
	config := &SMSConfig{
		AccessKeyId:     *accessKeyId,
		AccessKeySecret: *accessKeySecret,
		Endpoint:        *endpoint,
		SignName:        *signName,
		TemplateCode:    *templateCode,
	}

	// 创建短信发送器
	sender, err := NewSMSSender(config, phoneNumbers, time.Duration(*intervalMinutes)*time.Minute)
	if err != nil {
		log.Fatalf("创建短信发送器失败: %v", err)
	}

	// 设置信号处理
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	go func() {
		sig := <-sigChan
		log.Printf("收到信号 %v，正在优雅停止...", sig)
		cancel()
	}()

	// 启动服务
	sender.Start(ctx)
}
