#!/bin/bash

# SMS 周期性发送器启动脚本
# 使用方法: ./start.sh [参数]

# 设置脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BINARY_NAME="sms-sender"

# 检查二进制文件是否存在
if [ ! -f "${SCRIPT_DIR}/../../${BINARY_NAME}" ]; then
    echo "错误: 找不到可执行文件 ${BINARY_NAME}"
    echo "请先在项目根目录执行: go build -o ${BINARY_NAME} ./cmd/sms-sender"
    exit 1
fi

# 默认配置
DEFAULT_INTERVAL=5
DEFAULT_PHONES="13418852584,15914138067,14774972450,13725519305"
DEFAULT_SIGN_NAME="音活"
DEFAULT_TEMPLATE_CODE="SMS_315720114"

# 日志文件
LOG_FILE="${SCRIPT_DIR}/sms-sender.log"
PID_FILE="${SCRIPT_DIR}/sms-sender.pid"

# 函数：显示帮助信息
show_help() {
    echo "SMS 周期性发送器启动脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help              显示此帮助信息"
    echo "  -d, --daemon            后台运行模式"
    echo "  -s, --stop              停止后台运行的程序"
    echo "  -r, --restart           重启程序"
    echo "  --status                查看程序状态"
    echo "  --interval MINUTES      发送间隔（分钟），默认: ${DEFAULT_INTERVAL}"
    echo "  --phones PHONE_LIST     手机号码列表，用逗号分隔，默认: ${DEFAULT_PHONES}"
    echo "  --sign-name SIGN        短信签名，默认: ${DEFAULT_SIGN_NAME}"
    echo "  --template-code CODE    短信模板代码，默认: ${DEFAULT_TEMPLATE_CODE}"
    echo ""
    echo "示例:"
    echo "  $0                                    # 前台运行，使用默认配置"
    echo "  $0 -d                                 # 后台运行，使用默认配置"
    echo "  $0 -d --interval 10 --phones \"13800138000,13900139000\""
    echo "  $0 --status                           # 查看运行状态"
    echo "  $0 --stop                             # 停止后台程序"
    echo ""
}

# 函数：检查程序是否在运行
is_running() {
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if ps -p "$pid" > /dev/null 2>&1; then
            return 0
        else
            rm -f "$PID_FILE"
            return 1
        fi
    fi
    return 1
}

# 函数：停止程序
stop_program() {
    if is_running; then
        local pid=$(cat "$PID_FILE")
        echo "正在停止 SMS 发送器 (PID: $pid)..."
        kill "$pid"
        
        # 等待程序停止
        local count=0
        while is_running && [ $count -lt 30 ]; do
            sleep 1
            count=$((count + 1))
        done
        
        if is_running; then
            echo "程序未能正常停止，强制终止..."
            kill -9 "$pid"
            rm -f "$PID_FILE"
        else
            echo "SMS 发送器已停止"
        fi
    else
        echo "SMS 发送器未在运行"
    fi
}

# 函数：显示状态
show_status() {
    if is_running; then
        local pid=$(cat "$PID_FILE")
        echo "SMS 发送器正在运行 (PID: $pid)"
        echo "日志文件: $LOG_FILE"
        echo "最近几行日志:"
        tail -n 5 "$LOG_FILE" 2>/dev/null || echo "无法读取日志文件"
    else
        echo "SMS 发送器未在运行"
    fi
}

# 解析命令行参数
DAEMON_MODE=false
STOP_MODE=false
RESTART_MODE=false
STATUS_MODE=false
INTERVAL=""
PHONES=""
SIGN_NAME=""
TEMPLATE_CODE=""

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -d|--daemon)
            DAEMON_MODE=true
            shift
            ;;
        -s|--stop)
            STOP_MODE=true
            shift
            ;;
        -r|--restart)
            RESTART_MODE=true
            shift
            ;;
        --status)
            STATUS_MODE=true
            shift
            ;;
        --interval)
            INTERVAL="$2"
            shift 2
            ;;
        --phones)
            PHONES="$2"
            shift 2
            ;;
        --sign-name)
            SIGN_NAME="$2"
            shift 2
            ;;
        --template-code)
            TEMPLATE_CODE="$2"
            shift 2
            ;;
        *)
            echo "未知参数: $1"
            echo "使用 -h 或 --help 查看帮助信息"
            exit 1
            ;;
    esac
done

# 处理特殊模式
if [ "$STATUS_MODE" = true ]; then
    show_status
    exit 0
fi

if [ "$STOP_MODE" = true ]; then
    stop_program
    exit 0
fi

if [ "$RESTART_MODE" = true ]; then
    stop_program
    sleep 2
    DAEMON_MODE=true
fi

# 检查是否已在运行
if is_running && [ "$RESTART_MODE" = false ]; then
    echo "SMS 发送器已在运行 (PID: $(cat "$PID_FILE"))"
    echo "使用 --stop 停止或 --restart 重启"
    exit 1
fi

# 构建命令行参数
CMD_ARGS=""
if [ -n "$INTERVAL" ]; then
    CMD_ARGS="$CMD_ARGS -interval $INTERVAL"
fi
if [ -n "$PHONES" ]; then
    CMD_ARGS="$CMD_ARGS -phones \"$PHONES\""
fi
if [ -n "$SIGN_NAME" ]; then
    CMD_ARGS="$CMD_ARGS -sign-name \"$SIGN_NAME\""
fi
if [ -n "$TEMPLATE_CODE" ]; then
    CMD_ARGS="$CMD_ARGS -template-code \"$TEMPLATE_CODE\""
fi

# 启动程序
cd "${SCRIPT_DIR}/../.."

if [ "$DAEMON_MODE" = true ]; then
    echo "正在后台启动 SMS 发送器..."
    echo "日志文件: $LOG_FILE"
    
    # 后台运行
    nohup ./"${BINARY_NAME}" $CMD_ARGS > "$LOG_FILE" 2>&1 &
    echo $! > "$PID_FILE"
    
    sleep 2
    if is_running; then
        echo "SMS 发送器已启动 (PID: $(cat "$PID_FILE"))"
        echo "使用 '$0 --status' 查看状态"
        echo "使用 '$0 --stop' 停止程序"
    else
        echo "启动失败，请检查日志文件: $LOG_FILE"
        exit 1
    fi
else
    echo "正在前台启动 SMS 发送器..."
    echo "按 Ctrl+C 停止程序"
    exec ./"${BINARY_NAME}" $CMD_ARGS
fi
